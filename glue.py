# Copyright 2024 onwards Answer.AI, LightOn, and contributors
# License: Apache-2.0

# Copyright 2022 MosaicML Examples authors
# SPDX-License-Identifier: Apache-2.0

import copy
import gc
import multiprocessing as mp
import os
import pickle
import sys
import time
from collections import defaultdict
from multiprocessing import Pool
from multiprocessing.managers import Dict<PERSON><PERSON><PERSON>, SyncManager
from typing import Any, Dict, List, Optional, Sequence, Set, Tuple
from urllib.parse import urlparse

# Add folder root to path to allow us to use relative imports regardless of what directory the script is run from
sys.path.append(os.path.dirname(os.path.realpath(__file__)))

import numpy as np
import omegaconf as om
import src.evals.glue_jobs as glue_jobs_module
import src.evals.misc_jobs as misc_jobs_module
import src.evals.superglue_jobs as superglue_jobs_module
import src.hf_bert as hf_bert_module
import src.mosaic_bert as mosaic_bert_module
import src.flex_bert as flex_bert_module
import torch
from composer import algorithms
from composer.callbacks import (
    LRMonitor,
    MemoryMonitor,
    OptimizerMonitor,
    RuntimeEstimator,
    SpeedMonitor,
)
from composer.loggers import WandBLogger, TensorboardLogger
from composer.optim.scheduler import (
    ConstantWithWarmupScheduler,
    CosineAnnealingWithWarmupScheduler,
    LinearWithWarmupScheduler,
)
from src.scheduler import WarmupStableDecayScheduler
from composer.utils import reproducibility
from composer.utils.file_helpers import get_file
from composer.utils.object_store import S3ObjectStore
from omegaconf import DictConfig

TASK_NAME_TO_CLASS = {
    "mnli": glue_jobs_module.MNLIJob,
    "rte": glue_jobs_module.RTEJob,
    "mrpc": glue_jobs_module.MRPCJob,
    "qnli": glue_jobs_module.QNLIJob,
    "qqp": glue_jobs_module.QQPJob,
    "sst2": glue_jobs_module.SST2Job,
    "stsb": glue_jobs_module.STSBJob,
    "cola": glue_jobs_module.COLAJob,
    "boolq": superglue_jobs_module.BoolQJob,
    "cb": superglue_jobs_module.CBJob,
    "copa": superglue_jobs_module.COPAJob,
    "multirc": superglue_jobs_module.MultiRCJob,
    "wic": superglue_jobs_module.WiCJob,
    "swag": misc_jobs_module.SWAGJob,
    "eurlex": misc_jobs_module.EurlexJob,
}

GLUE_TASKS = {"mnli", "rte", "mrpc", "qnli", "qqp", "sst2", "stsb", "cola"}
SUPERGLUE_TASKS = {"boolq", "cb", "copa", "multirc", "rte", "wic"}


def build_algorithm(name, kwargs):
    if name == "gradient_clipping":
        return algorithms.GradientClipping(**kwargs)
    elif name == "alibi":
        return algorithms.Alibi(**kwargs)
    elif name == "gated_linear_units":
        return algorithms.GatedLinearUnits(**kwargs)
    else:
        raise ValueError(f"Not sure how to build algorithm: {name}")


def build_callback(name, kwargs):
    if name == "lr_monitor":
        return LRMonitor()
    elif name == "memory_monitor":
        return MemoryMonitor()
    elif name == "speed_monitor":
        return SpeedMonitor(
            window_size=kwargs.get("window_size", 1),
            gpu_flops_available=kwargs.get("gpu_flops_available", None),
        )
    elif name == "runtime_estimator":
        return RuntimeEstimator()
    elif name == "optimizer_monitor":
        return OptimizerMonitor(
            log_optimizer_metrics=kwargs.get("log_optimizer_metrics", True),
        )
    else:
        raise ValueError(f"Not sure how to build callback: {name}")


def build_logger(name, kwargs):
    if name == "wandb":
        # Make a copy of kwargs to avoid modifying the original
        kwargs_copy = kwargs.copy()
        return WandBLogger(**kwargs_copy)
    elif name == "tensorboard":
        # Make a copy of kwargs to avoid modifying the original
        kwargs_copy = kwargs.copy()

        # Handle tilde expansion for log_dir
        if "log_dir" in kwargs_copy and isinstance(kwargs_copy["log_dir"], str):
            # First, expand any variables in the path
            if "${run_name}" in kwargs_copy["log_dir"] and "run_name" in globals():
                kwargs_copy["log_dir"] = kwargs_copy["log_dir"].replace("${run_name}", globals()["run_name"])

            # Then handle tilde expansion
            if kwargs_copy["log_dir"].startswith("~"):
                kwargs_copy["log_dir"] = os.path.expanduser(kwargs_copy["log_dir"])

            # Ensure we don't have a nested tilde path issue
            if "/~/" in kwargs_copy["log_dir"]:
                kwargs_copy["log_dir"] = kwargs_copy["log_dir"].replace("/~/", "/")

            # Normalize the path to remove any redundancies
            kwargs_copy["log_dir"] = os.path.normpath(kwargs_copy["log_dir"])

            print(f"TensorBoard log_dir set to: {kwargs_copy['log_dir']}")

        # Ensure any file objects are properly closed before returning
        if "file_writer" in kwargs_copy:
            try:
                if hasattr(kwargs_copy["file_writer"], "close"):
                    kwargs_copy["file_writer"].close()
            except:
                pass
            # Remove the file_writer to avoid pickling issues
            del kwargs_copy["file_writer"]

        return TensorboardLogger(**kwargs_copy)
    else:
        raise ValueError(f"Not sure how to build logger: {name}")


def build_scheduler(cfg):
    if cfg.name == "constant_with_warmup":
        return ConstantWithWarmupScheduler(t_warmup=cfg.t_warmup)
    elif cfg.name == "cosine_with_warmup":
        return CosineAnnealingWithWarmupScheduler(
            t_warmup=cfg.t_warmup, alpha_f=cfg.alpha_f
        )
    elif cfg.name == "linear_decay_with_warmup":
        return LinearWithWarmupScheduler(t_warmup=cfg.t_warmup, alpha_f=cfg.alpha_f)
    elif cfg.name == "warmup_stable_decay":
        return WarmupStableDecayScheduler(t_warmup=cfg.t_warmup, alpha_f=cfg.alpha_f)
    else:
        raise ValueError(f"Not sure how to build scheduler: {cfg.name}")


def build_model(
    cfg: DictConfig, num_labels: int, multiple_choice: bool = False, **kwargs
):
    if cfg.name == "hf_bert":
        return hf_bert_module.create_hf_bert_classification(
            num_labels=num_labels,
            pretrained_model_name=cfg.pretrained_model_name,
            use_pretrained=cfg.get("use_pretrained", False),
            model_config=cfg.get("model_config", None),
            tokenizer_name=cfg.get("tokenizer_name", None),
            gradient_checkpointing=cfg.get("gradient_checkpointing", None),
            multiple_choice=multiple_choice,
            **kwargs,
        )
    elif cfg.name == "mosaic_bert":
        return mosaic_bert_module.create_mosaic_bert_classification(
            num_labels=num_labels,
            pretrained_model_name=cfg.pretrained_model_name,
            pretrained_checkpoint=cfg.get("pretrained_checkpoint", None),
            model_config=cfg.get("model_config", None),
            tokenizer_name=cfg.get("tokenizer_name", None),
            gradient_checkpointing=cfg.get("gradient_checkpointing", None),
            multiple_choice=multiple_choice,
            **kwargs,
        )
    elif cfg.name == "flex_bert":
        return flex_bert_module.create_flex_bert_classification(
            num_labels=num_labels,
            pretrained_model_name=cfg.pretrained_model_name,
            pretrained_checkpoint=cfg.get("pretrained_checkpoint", None),
            model_config=cfg.get("model_config", None),
            tokenizer_name=cfg.get("tokenizer_name", None),
            gradient_checkpointing=cfg.get("gradient_checkpointing", None),
            multiple_choice=multiple_choice,
            **kwargs,
        )
    else:
        raise ValueError(f"Not sure how to build model with name={cfg.name}")


def get_values_from_path(path: str, separator: str = "/") -> Dict[str, str]:
    """Parses out information from a path/string that looks like.

    ...<separator>key=value<separator...
    """
    dict_output = {}
    underscore_split = path.split(separator)
    for item in underscore_split:
        if "=" not in item:
            continue

        key, value = item.split("=")
        dict_output[key] = value
    return dict_output


def get_checkpoint_name_from_path(path: str) -> str:
    """To go from checkpoint name to path, replace | with /"""
    return path.lstrip("/").replace("/", "|")


def download_starting_checkpoint(
    starting_checkpoint_load_path: str, local_pretrain_checkpoints_folder: str
) -> str:
    """Downloads the pretrained checkpoints to start from.

    Currently only supports S3 and URLs
    """
    load_object_store = None
    parsed_path = urlparse(starting_checkpoint_load_path)
    if parsed_path.scheme == "s3":
        load_object_store = S3ObjectStore(bucket=parsed_path.netloc)

    download_path = (
        parsed_path.path
        if parsed_path.scheme == "s3"
        else starting_checkpoint_load_path
    )
    os.makedirs(local_pretrain_checkpoints_folder, exist_ok=True)
    local_path = os.path.join(
        local_pretrain_checkpoints_folder,
        get_checkpoint_name_from_path(parsed_path.path),
    )

    # Check if the file exists at the original path (for local files)
    if os.path.exists(starting_checkpoint_load_path):
        print(f"Using existing checkpoint at {starting_checkpoint_load_path}")
        return starting_checkpoint_load_path

    # Check if the file exists at the local path
    if os.path.exists(local_path):
        print(f"Using existing checkpoint at {local_path}")
        return local_path

    # For S3 paths, we need to strip the leading slash
    # For local paths, we should keep the leading slash
    path_to_get = download_path
    if parsed_path.scheme == "s3":
        path_to_get = download_path.lstrip("/")

    try:
        get_file(
            destination=local_path,
            path=path_to_get,
            object_store=load_object_store,
            progress_bar=True,
        )
    except FileNotFoundError:
        print(f"Warning: Checkpoint not found at {path_to_get}")
        print(f"Please ensure the checkpoint file exists at {starting_checkpoint_load_path}")
        raise

    return local_path


def _setup_gpu_queue(num_gpus: int, manager: SyncManager):
    """Returns a queue with [0, 1, ..

    num_gpus].
    """
    gpu_queue = manager.Queue(num_gpus)
    for gpu_id in range(num_gpus):
        gpu_queue.put(gpu_id)
    return gpu_queue


def create_job_configs(
    main_config: om.DictConfig,
    tasks_to_run: Set[str],
    pretrained_checkpoint_path: Optional[str],
):
    configs = []
    for task_name, task_config in main_config.tasks.items():
        if main_config.get("base_run_name") is None:
            main_config.base_run_name = os.environ.get("COMPOSER_RUN_NAME", "glue")
        if task_name not in tasks_to_run:
            continue
        for task_seed in task_config.get("seeds", [main_config.default_seed]):
            run_name = (
                f"{main_config.base_run_name}_task={task_name}_seed={str(task_seed)}"
            )
            logger_configs = copy.deepcopy(main_config.get("loggers", {}))
            for logger_name, logger_config in logger_configs.items():
                if logger_name == "wandb":
                    # allow user set groups, otherwise set group to run name
                    if "group" not in logger_config:
                        logger_config["group"] = main_config.base_run_name
                    logger_config["name"] = run_name

            model_kwargs = copy.deepcopy(main_config.model) # Create a copy of model config to avoid modifying the main_config
            if "model_config" not in model_kwargs:
                model_kwargs.model_config = {}
            model_kwargs.model_config.update(task_config.get("model_config", {})) # update with task specific model config

            task_seed_config = om.OmegaConf.create(
                {
                    "task": task_name,
                    "job_name": run_name,
                    "seed": task_seed,
                    "model": model_kwargs,
                    "tokenizer_name": main_config.tokenizer_name,
                    "scheduler": main_config.scheduler,
                    "load_path": pretrained_checkpoint_path,
                    "save_folder": os.path.join(
                        main_config.save_finetune_checkpoint_folder,
                        f"task={task_name}",
                        f"seed={task_seed}",
                    ),
                    "loggers": logger_configs,
                    "callbacks": main_config.get("callbacks", {}),
                    "algorithms": main_config.get("algorithms", {}),
                    "precision": main_config.get("precision", None),
                    "trainer_kwargs": task_config.trainer_kwargs,
                }
            )
            configs.append(task_seed_config)

    return configs

def run_job_worker(
    config: om.DictConfig,
    gpu_queue: Optional[mp.Queue] = None,
    process_to_gpu: Optional[DictProxy] = None,
) -> Any:
    """Instantiates the job object and runs it."""
    try:
        # need to set seed before model initialization for determinism
        reproducibility.seed_all(config.seed)
        task_cls = TASK_NAME_TO_CLASS[config.task]
        instantiated_job = task_cls(
            job_name=config.job_name,
            seed=config.seed,
            model=build_model(
                config.model,
                num_labels=task_cls.num_labels,
                multiple_choice=task_cls.multiple_choice,
                custom_eval_metrics=task_cls.custom_eval_metrics,
            ),
            tokenizer_name=config.tokenizer_name,
            scheduler=build_scheduler(config.scheduler),
            load_path=config.load_path,
            save_folder=config.save_folder,
            loggers=[
                build_logger(name, logger_config)
                for name, logger_config in config.get("loggers", {}).items()
            ],
            callbacks=[
                build_callback(name, callback_config)
                for name, callback_config in config.get("callbacks", {}).items()
            ],
            algorithms=[
                build_algorithm(name, algorithm_config)
                for name, algorithm_config in config.get("algorithms", {}).items()
            ],
            precision=config.precision,
            **config.trainer_kwargs,
        )
        results = instantiated_job.run(gpu_queue, process_to_gpu)

        # delete the job so that the optimizer and anything else on the gpu gets deleted
        del instantiated_job
        torch.cuda.empty_cache()
        gc.collect()
        return results
    except Exception as e:
        print(f"Error in job worker for {config.get('job_name', 'unknown')}: {e}")
        # Return a minimal result structure to avoid breaking the results processing
        return {
            "job_name": config.get("job_name", "unknown"),
            "metrics": {},
            "checkpoints": [],
            "error": str(e)
        }


def run_jobs_parallel(configs: Sequence[om.DictConfig]) -> Dict[str, Any]:
    """Runs a list of jobs (passed in as Hydra configs) across GPUs.

    Returns a dictionary mapping job name to the result and original config
    Each job's results is a dict of:

    * 'checkpoints': list of saved_checkpoints, if any,
    * 'metrics': nested dict of results, accessed by
                 dataset and metric name, e.g.
                 ``metrics['glue_mnli']['MulticlassAccuracy']``.
    * 'job_name': The job name, helpful for keeping track of results during multiprocessing
    """
    num_gpus = torch.cuda.device_count()
    results = []

    # Use serial execution if only one GPU is available to avoid multiprocessing overhead
    if num_gpus == 1 and len(configs) > 0:
        print("Only one GPU available, running jobs serially to avoid multiprocessing overhead")
        return run_jobs_serial(configs)

    try:
        with mp.Manager() as manager:
            # workers get gpu ids from this queue
            # to set the GPU to run on
            gpu_queue = _setup_gpu_queue(num_gpus, manager)
            process_to_gpu = manager.dict()

            ctx = mp.get_context("spawn")
            with Pool(max_workers=min(num_gpus, len(configs)), mp_context=ctx) as pool:
                # Use starmap to properly pass multiple arguments to each worker
                args_list = [(config, gpu_queue, process_to_gpu) for config in configs]
                results = pool.starmap(run_job_worker, args_list)
    except (pickle.PickleError, TypeError) as e:
        print(f"Error in parallel execution: {e}")
        print("Falling back to serial execution")
        return run_jobs_serial(configs)

    job_name_to_config = {config.job_name: config for config in configs}
    finished_results = {}
    for result in results:
        if result is None:
            print("Warning: Received None result from worker")
            continue
        
        job_name = result.get("job_name", "unknown")
        if "error" in result:
            print(f"Job {job_name} failed with error: {result['error']}")
            continue
            
        finished_results[job_name] = {
            "result": result,
            "config": job_name_to_config.get(job_name, {}),
        }

    return finished_results


def run_jobs_serial(configs) -> Dict[str, Any]:
    """Runs the jobs serially, rather than in parallel.

    Useful for debugging
    """
    results = {}
    for config in configs:
        result = run_job_worker(config)
        results[config.job_name] = {"result": result, "config": config}
    return results


def format_job_name(job_name: str) -> str:
    """Formats the job name for pretty printing."""
    dict_output = get_values_from_path(job_name, separator="_")
    return f'{dict_output["task"].upper()}(seed={dict_output["seed"]})'


def _write_to_file(content: str, file_path: str, mode: str = "a"):
    """Writes content to a file.

    Args:
        content: The content to write
        file_path: Path to the file
        mode: File open mode ('a' for append, 'w' for write)
    """
    os.makedirs(os.path.dirname(os.path.abspath(file_path)), exist_ok=True)
    with open(file_path, mode) as f:
        f.write(content)

def _print_table(results: Dict[str, Dict[str, Any]], results_file: Optional[str] = None):
    """Pretty prints a table given a results dictionary and optionally writes to a file.

    Args:
        results: Dictionary of results
        results_file: Optional path to write results to
    """
    header = "{job_name:50}| {eval_task:25}| {name:27}|"
    hyphen_count = 50 + 25 + 27 + 11
    row_format = header + " {value:.2f}"

    output = []
    output.append("\nCollected Job Results: \n")
    output.append("-" * hyphen_count)
    output.append(header.format(job_name="Job", eval_task="Dataset", name="Metric"))
    output.append("-" * hyphen_count)

    for job_name, result in results.items():
        for eval_task, eval_results in result["result"]["metrics"].items():
            for name, metric in eval_results.items():
                output.append(
                    row_format.format(
                        job_name=format_job_name(job_name),
                        eval_task=eval_task,
                        name=name,
                        value=metric * 100,
                    )
                )

    output.append("-" * hyphen_count)
    output.append("\n")

    # Join all lines and print to console
    output_str = "\n".join(output)
    print(output_str)

    # Write to file if specified
    if results_file:
        _write_to_file(output_str, results_file)


def _print_averaged_glue_results(glue_results: List[Tuple[str, float]], results_file: Optional[str] = None) -> None:
    """Pretty prints a table of glue results averaged across seeds and optionally writes to a file.

    Args:
        glue_results: List of tuples containing task name and result
        results_file: Optional path to write results to
    """
    header = "{job_name:50}|"
    hyphen_count = 50 + 11
    row_format = header + " {value:.2f}"

    output = []
    output.append("\nCollected Job Results: \n")
    output.append("-" * hyphen_count)
    output.append(header.format(job_name="Task"))
    output.append("-" * hyphen_count)

    for task_name, result in glue_results:
        output.append(
            row_format.format(
                job_name=f"{task_name.upper()}",
                value=result,
            )
        )

    output.append("-" * hyphen_count)
    output.append("\n")

    # Join all lines and print to console
    output_str = "\n".join(output)
    print(output_str)

    # Write to file if specified
    if results_file:
        _write_to_file(output_str, results_file)


def train(config: om.DictConfig) -> None:
    """Main training logic.

    Args:
        config (DictConfig): Configuration composed by OmegaConf
    """
    # Get results file path from config if specified
    results_file = config.get("results_file", None)
    start_time = time.time()

    # Initial default seed
    reproducibility.seed_all(config.default_seed)

    # Quiet down WandB
    os.environ["WANDB_SILENT"] = "true"

    # Set tokenizer parallelism
    os.environ["TOKENIZERS_PARALLELISM"] = "false"

    # Confirm GPUs if parallel=True
    if config.parallel:
        assert (
            torch.cuda.device_count() > 0
        ), "Can only use parallel mode if GPUs are available. Please set parallel=False."

    # Downloads the starting checkpoint ahead of time so that
    # the different tasks don't all try to download it at the same time
    if config.get("starting_checkpoint_load_path", None):
        local_pretrain_checkpoint_path = download_starting_checkpoint(
            config.starting_checkpoint_load_path,
            config.local_pretrain_checkpoint_folder,
        )
    else:
        local_pretrain_checkpoint_path = None

    # Builds round 1 configs and runs them
    round_1_task_names = {
        # glue:
        *{"cola", "sst2", "qqp", "qnli", "mnli"},
        # superglue:
        *{"boolq", "cb", "multirc", "wic"},
        # misc:
        *{"swag", "eurlex"},
    }
    round_1_job_configs = create_job_configs(
        config, round_1_task_names, local_pretrain_checkpoint_path
    )

    round_1_results = {}
    if len(round_1_job_configs) > 0:
        if config.parallel:
            round_1_results = run_jobs_parallel(round_1_job_configs)
        else:
            round_1_results = run_jobs_serial(round_1_job_configs)

    # Builds up the information needed to run the second round, starting from the MNLI checkpoints
    checkpoint_paths = {}
    for job_name, output_dict in round_1_results.items():
        job_results = output_dict["result"]
        job_values = get_values_from_path(job_name, separator="_")
        task_name = job_values["task"]

        if task_name in checkpoint_paths:
            continue
        elif len(job_results["checkpoints"]) == 0:
            continue

        checkpoint_paths[task_name] = job_results["checkpoints"][-1]

    # Builds round 2 configs and runs them
    round_2_task_names = {
        "mnli": {"rte", "mrpc", "stsb"},
        "swag": {"copa"},
    }
    round_2_job_configs = []
    for dependent_task_name in round_2_task_names:
        starting_checkpoint_path = (
            checkpoint_paths[dependent_task_name]
            if dependent_task_name in checkpoint_paths
            else local_pretrain_checkpoint_path
        )
        round_2_job_configs.extend(
            create_job_configs(
                config,
                round_2_task_names[dependent_task_name],
                starting_checkpoint_path,
            )
        )

    round_2_results = {}
    if len(round_2_job_configs) > 0:
        if config.parallel:
            round_2_results = run_jobs_parallel(round_2_job_configs)
        else:
            round_2_results = run_jobs_serial(round_2_job_configs)

    end_time = time.time()
    training_time = end_time - start_time

    # Format the training time information
    time_info = f"\n{'-' * 30}\nTraining completed in {training_time:.2f} seconds\n{'-' * 30}\n"
    print(time_info)

    # Write training time to results file if specified
    if results_file:
        # Add timestamp to the beginning of the file
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        header = f"# GLUE Benchmark Results - {timestamp}\n\n"
        _write_to_file(header + time_info, results_file, mode="w")

    # Join the results and pretty print them
    all_results = {}
    all_results.update(round_1_results)
    all_results.update(round_2_results)
    _print_table(all_results, results_file)

    # Average the GLUE results across seeds and pretty print them
    glue_results: Dict[str, List[float]] = defaultdict(list)
    for job_name, result in all_results.items():
        job_values = get_values_from_path(job_name, separator="_")
        for _, eval_results in result["result"]["metrics"].items():
            for _, metric in eval_results.items():
                glue_results[job_values["task"]].append(metric * 100)
    results_mean: Dict[str, float] = {
        key: float(np.mean(values)) for key, values in glue_results.items()
    }

    overall_glue = []
    overall_superglue = []
    overall_other = []
    for task_name, average_metric in results_mean.items():
        if task_name in GLUE_TASKS:
            overall_glue.append(average_metric)
        if task_name in SUPERGLUE_TASKS:
            overall_superglue.append(average_metric)
        if task_name not in GLUE_TASKS.union(SUPERGLUE_TASKS):
            overall_other.append(average_metric)

    if len(overall_other) > 0:
        other_results_mean = {
            k: v
            for k, v in results_mean.items()
            if k not in GLUE_TASKS.union(SUPERGLUE_TASKS)
        }
        _print_averaged_glue_results(
            [(key, value) for key, value in other_results_mean.items()],
            results_file
        )

    if len(overall_glue) > 0:
        glue_results_mean = {
            **{k: v for k, v in results_mean.items() if k in GLUE_TASKS},
            "glue": float(np.mean(overall_glue)),
        }
        _print_averaged_glue_results(
            [(key, value) for key, value in glue_results_mean.items()],
            results_file
        )

    if len(overall_superglue) > 0:
        superglue_results_mean = {
            **{k: v for k, v in results_mean.items() if k in SUPERGLUE_TASKS},
            "superglue": float(np.mean(overall_superglue)),
        }
        _print_averaged_glue_results(
            [(key, value) for key, value in superglue_results_mean.items()],
            results_file
        )


if __name__ == "__main__":
    yaml_path, args_list = sys.argv[1], sys.argv[2:]

    with open(yaml_path) as f:
        yaml_cfg = om.OmegaConf.load(f)

    cli_cfg = om.OmegaConf.from_cli(args_list)
    cfg = om.OmegaConf.merge(yaml_cfg, cli_cfg)

    if cfg.model.name == "mosaic_bert":
        with open("yamls/defaults.yaml") as f:
            default_cfg = om.OmegaConf.load(f)
        cfg = om.OmegaConf.merge(cfg, default_cfg)

    assert isinstance(cfg, om.DictConfig)
    train(cfg)
