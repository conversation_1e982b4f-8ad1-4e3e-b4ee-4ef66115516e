#!/usr/bin/env python3
"""
Debug script to compare model states and inputs between simple and inference approaches.
"""

import os
import sys
import torch
from pathlib import Path

# Add src directory to path
sys.path.append(os.path.dirname(os.path.realpath(__file__)))

# Import our simple implementation
from src.bert_layers.configuration_bert import FlexBertConfig
from src.bert_layers.model import FlexBertForMaskedLM
from transformers import AutoTokenizer
from omegaconf import OmegaConf

# Import inference directory implementation
sys.path.insert(0, str(Path.cwd()))
from inference.inference import ModernBERTInference

# Configuration
CONFIG_PATH = "yamls/main/flex-bert-modernbert-base-edu-fw-320B-paper-custom-tokenizer.yaml"
CHECKPOINT_PATH = "/s2_nfs/ckpt/flex-bert-modernbert-base-edu-fw-320B-article-custom-tokenizer/ep3-ba339282-rank0.pt"
INPUT_TEXT = "The capital of France is <mask>."
DEVICE = "cuda" if torch.cuda.is_available() else "cpu"

def load_config(config_path: str):
    """Load configuration like our simple script does."""
    config_path = Path(config_path)
    with open(config_path) as f:
        config = OmegaConf.load(f)
    
    defaults_path = config_path.parent.parent / "defaults.yaml"
    if defaults_path.exists():
        with open(defaults_path) as f:
            default_config = OmegaConf.load(f)
        config = OmegaConf.merge(default_config, config)
    
    OmegaConf.resolve(config)
    return OmegaConf.to_container(config, resolve=True)

def debug_model_states():
    """Compare model states between approaches."""
    print("🔍 MODEL STATE COMPARISON DEBUG")
    print("=" * 60)
    
    # Load simple approach model
    print("\n--- Loading Simple Approach Model ---")
    config = load_config(CONFIG_PATH)
    tokenizer_name = config.get("tokenizer_name", "bert-base-uncased")
    
    try:
        tokenizer = AutoTokenizer.from_pretrained(
            tokenizer_name,
            use_fast=True,
            trust_remote_code=True
        )
    except:
        tokenizer = AutoTokenizer.from_pretrained("bert-base-uncased")
    
    # Create model config
    model_config = config["model"]["model_config"]
    config_dict = dict(model_config)
    config_dict['vocab_size'] = len(tokenizer)
    bert_config = FlexBertConfig(**config_dict)
    
    # Create and load model
    simple_model = FlexBertForMaskedLM(bert_config)
    checkpoint = torch.load(CHECKPOINT_PATH, map_location="cpu")
    
    if "state" in checkpoint and "model" in checkpoint["state"]:
        state_dict = checkpoint["state"]["model"]
    else:
        state_dict = checkpoint
    
    # Remove model. prefix if present
    if any(key.startswith("model.") for key in state_dict.keys()):
        new_state_dict = {}
        for key, value in state_dict.items():
            if key.startswith("model."):
                new_key = key[6:]
                new_state_dict[new_key] = value
            else:
                new_state_dict[key] = value
        state_dict = new_state_dict
    
    simple_model.load_state_dict(state_dict)
    simple_model = simple_model.to(DEVICE)
    simple_model.eval()
    
    print(f"Simple model loaded: {sum(p.numel() for p in simple_model.parameters())} parameters")
    
    # Load inference directory model
    print("\n--- Loading Inference Directory Model ---")
    with ModernBERTInference(
        config_path=CONFIG_PATH,
        checkpoint_path=CHECKPOINT_PATH,
        device="auto",
        precision="fp32"
    ) as inference:
        
        mlm_pipeline = inference._get_mlm_pipeline()
        inference_model = mlm_pipeline.model
        
        print(f"Inference model loaded: {sum(p.numel() for p in inference_model.parameters())} parameters")
        
        # Compare model parameters
        print("\n--- Comparing Model Parameters ---")
        param_diff_count = 0
        max_diff = 0.0
        
        simple_params = dict(simple_model.named_parameters())
        inference_params = dict(inference_model.named_parameters())
        
        for name, simple_param in simple_params.items():
            if name in inference_params:
                inference_param = inference_params[name]
                diff = torch.abs(simple_param - inference_param).max().item()
                max_diff = max(max_diff, diff)
                if diff > 1e-6:
                    param_diff_count += 1
                    print(f"  Parameter {name}: max diff = {diff:.8f}")
        
        print(f"Parameters with differences > 1e-6: {param_diff_count}")
        print(f"Maximum parameter difference: {max_diff:.8f}")
        
        if max_diff < 1e-6:
            print("✅ Model parameters are identical!")
        else:
            print("❌ Model parameters differ!")
        
        # Compare model configurations
        print("\n--- Comparing Model Configurations ---")
        print(f"Simple model config: {bert_config}")
        print(f"Inference model config: {inference_model.config}")
        
        # Test with identical inputs
        print("\n--- Testing with Identical Inputs ---")
        
        # Prepare padded inputs (like simple approach)
        inputs_padded = tokenizer(INPUT_TEXT, return_tensors="pt", padding=True, truncation=True)
        inputs_padded = {k: v.to(DEVICE) for k, v in inputs_padded.items()}
        
        print(f"Padded input shape: {inputs_padded['input_ids'].shape}")
        print(f"Padded input IDs: {inputs_padded['input_ids']}")
        
        # Run simple model
        with torch.no_grad():
            simple_outputs = simple_model(**inputs_padded)
            simple_logits = simple_outputs.logits
        
        print(f"Simple model output shape: {simple_logits.shape}")
        
        # Run inference model with same padded inputs
        with torch.no_grad():
            inference_outputs = inference_model(**inputs_padded)
            inference_logits = inference_outputs.logits
        
        print(f"Inference model output shape: {inference_logits.shape}")
        
        # Compare logits
        logits_diff = torch.abs(simple_logits - inference_logits).max().item()
        print(f"Max logits difference with padded inputs: {logits_diff:.8f}")
        
        if logits_diff < 1e-5:
            print("✅ Logits are identical with padded inputs!")
        else:
            print("❌ Logits differ with padded inputs!")
        
        # Find mask position and compare specific logits
        mask_token_id = tokenizer.mask_token_id
        mask_positions = (inputs_padded["input_ids"] == mask_token_id).nonzero(as_tuple=True)
        
        if len(mask_positions[0]) > 0:
            batch_idx, seq_pos = mask_positions[0][0], mask_positions[1][0]
            
            simple_mask_logits = simple_logits[batch_idx, seq_pos]
            inference_mask_logits = inference_logits[batch_idx, seq_pos]
            
            print(f"\n--- Mask Position Logits Comparison ---")
            print(f"Mask position: ({batch_idx}, {seq_pos})")
            print(f"Simple logits for token 260: {simple_mask_logits[260]:.6f}")
            print(f"Simple logits for token 47304: {simple_mask_logits[47304]:.6f}")
            print(f"Inference logits for token 260: {inference_mask_logits[260]:.6f}")
            print(f"Inference logits for token 47304: {inference_mask_logits[47304]:.6f}")
            
            mask_logits_diff = torch.abs(simple_mask_logits - inference_mask_logits).max().item()
            print(f"Max mask logits difference: {mask_logits_diff:.8f}")

if __name__ == "__main__":
    debug_model_states()
