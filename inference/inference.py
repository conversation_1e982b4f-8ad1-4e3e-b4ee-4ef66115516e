# Copyright 2024 onwards Answer.AI, LightOn, and contributors
# License: Apache-2.0

import os
import sys
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
import numpy as np
import logging

# Add project root to path for imports
sys.path.append(str(Path(__file__).parent.parent))

# Handle imports - try relative first, then absolute
try:
    from .config.model_config import ModelConfig
    from .config.inference_config import InferenceConfig
    from .pipelines.mlm_pipeline import MLM<PERSON>ipeline
    from .pipelines.embedding_pipeline import EmbeddingPipeline
except ImportError:
    # If relative imports fail, try direct module imports
    import sys
    inference_dir = Path(__file__).parent
    sys.path.insert(0, str(inference_dir))
    
    from config.model_config import ModelConfig
    from config.inference_config import InferenceConfig
    from pipelines.mlm_pipeline import MLMPipeline
    from pipelines.embedding_pipeline import EmbeddingPipeline

logger = logging.getLogger(__name__)


class ModernBERTInference:
    """
    Unified inference interface for ModernBERT models.
    
    Provides both MLM (Masked Language Modeling) and embedding generation
    capabilities with automatic adaptation to padding/unpadding configuration.
    
    Example:
        ```python
        # Initialize from training config and checkpoint
        inference = ModernBERTInference(
            config_path="path/to/training_config.yaml",
            checkpoint_path="path/to/checkpoint.pt"
        )
        
        # MLM inference
        results = inference.predict_masked_tokens(
            "The capital of France is [MASK]."
        )
        
        # Embedding generation
        embeddings = inference.encode_texts([
            "This is a sentence.",
            "This is another sentence."
        ])
        ```
    """
    
    def __init__(
        self,
        config_path: str,
        checkpoint_path: str,
        device: str = "auto",
        precision: str = "fp32",
        **inference_kwargs
    ):
        """
        Initialize ModernBERT inference.
        
        Args:
            config_path: Path to the training YAML configuration file
            checkpoint_path: Path to the model checkpoint file
            device: Device to run inference on ("auto", "cpu", "cuda", etc.)
            precision: Precision to use ("fp32", "fp16", "bf16")
            **inference_kwargs: Additional inference configuration parameters
        """
        self.config_path = config_path
        self.checkpoint_path = checkpoint_path
        
        # Load configurations
        self.model_config = ModelConfig(config_path)
        self.inference_config = InferenceConfig(
            device=device,
            precision=precision,
            **inference_kwargs
        )
        
        # Initialize pipelines
        self.mlm_pipeline: Optional[MLMPipeline] = None
        self.embedding_pipeline: Optional[EmbeddingPipeline] = None
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        logger.info("ModernBERT Inference initialized")
        logger.info(f"Model config: padding={self.model_config.padding}, "
                   f"unpad_embeddings={self.model_config.unpad_embeddings}")
        logger.info(f"Inference config: device={self.inference_config.device}, "
                   f"precision={self.inference_config.precision}")
    
    def _get_mlm_pipeline(self) -> MLMPipeline:
        """Get or create MLM pipeline."""
        if self.mlm_pipeline is None:
            logger.info("Initializing MLM pipeline...")
            self.mlm_pipeline = MLMPipeline(
                model_config=self.model_config,
                inference_config=self.inference_config,
                checkpoint_path=self.checkpoint_path
            )
        return self.mlm_pipeline
    
    def _get_embedding_pipeline(self) -> EmbeddingPipeline:
        """Get or create embedding pipeline."""
        if self.embedding_pipeline is None:
            logger.info("Initializing embedding pipeline...")
            self.embedding_pipeline = EmbeddingPipeline(
                model_config=self.model_config,
                inference_config=self.inference_config,
                checkpoint_path=self.checkpoint_path
            )
        return self.embedding_pipeline
    
    # MLM Interface Methods
    def predict_masked_tokens(
        self,
        texts: Union[str, List[str]],
        top_k: int = 10,
        top_p: float = 0.9,
        temperature: float = 1.0,
        **kwargs
    ) -> Union[Dict[str, Any], List[Dict[str, Any]]]:
        """
        Predict masked tokens in input text(s).
        
        Args:
            texts: Input text(s) containing [MASK] tokens
            top_k: Number of top predictions to return
            top_p: Nucleus sampling threshold
            temperature: Sampling temperature
            **kwargs: Additional prediction parameters
            
        Returns:
            Prediction results for masked tokens
        """
        pipeline = self._get_mlm_pipeline()
        return pipeline.predict(
            inputs=texts,
            top_k=top_k,
            top_p=top_p,
            temperature=temperature,
            **kwargs
        )
    
    def fill_mask(
        self,
        text: str,
        mask_token: str = "[MASK]",
        return_top_k: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Fill masked tokens (Hugging Face pipeline compatible interface).
        
        Args:
            text: Input text with mask tokens
            mask_token: Mask token to replace
            return_top_k: Number of top predictions to return
            
        Returns:
            List of predictions with scores
        """
        pipeline = self._get_mlm_pipeline()
        return pipeline.fill_mask(text, mask_token, return_top_k)
    
    def batch_predict_masked_tokens(
        self,
        texts: List[str],
        batch_size: Optional[int] = None,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Predict masked tokens in batches for efficiency.
        
        Args:
            texts: List of input texts
            batch_size: Batch size for processing
            **kwargs: Additional prediction parameters
            
        Returns:
            List of prediction results
        """
        pipeline = self._get_mlm_pipeline()
        return pipeline.batch_predict(texts, batch_size, **kwargs)
    
    def evaluate_perplexity(
        self,
        texts: List[str],
        mask_probability: float = 0.15
    ) -> List[Dict[str, Any]]:
        """
        Evaluate perplexity of texts.
        
        Args:
            texts: Input texts to evaluate
            mask_probability: Probability of masking each token
            
        Returns:
            List of perplexity evaluations
        """
        pipeline = self._get_mlm_pipeline()
        return pipeline.evaluate_perplexity(texts, mask_probability)
    
    # Embedding Interface Methods
    def encode_texts(
        self,
        texts: Union[str, List[str]],
        pooling_strategy: str = "mean",
        normalize: bool = True,
        batch_size: Optional[int] = None,
        **kwargs
    ) -> np.ndarray:
        """
        Encode texts to embeddings.
        
        Args:
            texts: Input text(s) to encode
            pooling_strategy: Pooling strategy ("mean", "cls", "max", "mean_sqrt_len")
            normalize: Whether to L2 normalize embeddings
            batch_size: Batch size for processing
            **kwargs: Additional encoding parameters
            
        Returns:
            Embedding matrix [num_texts, embedding_dim]
        """
        pipeline = self._get_embedding_pipeline()
        
        if batch_size is not None:
            return pipeline.encode(texts, batch_size=batch_size, 
                                 pooling_strategy=pooling_strategy, 
                                 normalize=normalize, **kwargs)
        else:
            return pipeline.predict(texts, pooling_strategy=pooling_strategy, 
                                  normalize=normalize, **kwargs)
    
    def compute_similarity(
        self,
        texts1: Union[str, List[str]],
        texts2: Union[str, List[str]],
        similarity_fn: str = "cosine"
    ) -> Union[float, np.ndarray]:
        """
        Compute similarity between texts.
        
        Args:
            texts1: First set of texts
            texts2: Second set of texts
            similarity_fn: Similarity function ("cosine", "dot", "euclidean")
            
        Returns:
            Similarity scores
        """
        pipeline = self._get_embedding_pipeline()
        return pipeline.similarity(texts1, texts2, similarity_fn)
    
    def batch_encode_texts(
        self,
        texts: List[str],
        batch_size: Optional[int] = None,
        **kwargs
    ) -> np.ndarray:
        """
        Encode texts in batches for efficiency.
        
        Args:
            texts: List of input texts
            batch_size: Batch size for processing
            **kwargs: Additional encoding parameters
            
        Returns:
            Embedding matrix
        """
        pipeline = self._get_embedding_pipeline()
        return pipeline.encode(texts, batch_size=batch_size, **kwargs)
    
    # Utility Methods
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the loaded model.

        Returns:
            Dictionary with model information
        """
        # Get info from whichever pipeline is available
        if self.mlm_pipeline is not None:
            return self.mlm_pipeline.get_model_info()
        elif self.embedding_pipeline is not None:
            return self.embedding_pipeline.get_model_info()
        else:
            # Return basic info with correct padding information
            return {
                "status": "not_initialized",
                "config_path": self.config_path,
                "checkpoint_path": self.checkpoint_path,
                "is_unpadded": self.model_config.is_unpadded,
                "padding": self.model_config.padding,
                "hidden_size": self.model_config.hidden_size,
                "num_hidden_layers": self.model_config.num_hidden_layers,
                "model_config": self.model_config.to_dict(),
                "inference_config": self.inference_config.to_dict(),
            }
    
    def get_tokenizer_info(self) -> Dict[str, Any]:
        """
        Get information about the tokenizer.
        
        Returns:
            Dictionary with tokenizer information
        """
        # Get info from whichever pipeline is available
        if self.mlm_pipeline is not None:
            return self.mlm_pipeline.get_tokenizer_info()
        elif self.embedding_pipeline is not None:
            return self.embedding_pipeline.get_tokenizer_info()
        else:
            return {"status": "not_initialized"}
    
    def validate_configuration(self) -> bool:
        """
        Validate the inference configuration.
        
        Returns:
            True if configuration is valid
        """
        try:
            # Validate model config against checkpoint
            from .core.model_factory import ModelFactory
            factory = ModelFactory(self.model_config, self.inference_config)
            
            if not factory.validate_model_config_compatibility(self.checkpoint_path):
                logger.error("Model configuration incompatible with checkpoint")
                return False
            
            logger.info("Configuration validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            return False
    
    def get_embedding_dimension(self) -> int:
        """
        Get the dimension of embeddings produced by this model.
        
        Returns:
            Embedding dimension
        """
        return self.model_config.hidden_size
    
    def cleanup(self):
        """Clean up resources."""
        if self.mlm_pipeline is not None:
            self.mlm_pipeline.cleanup()
            self.mlm_pipeline = None
        
        if self.embedding_pipeline is not None:
            self.embedding_pipeline.cleanup()
            self.embedding_pipeline = None
        
        logger.info("ModernBERT Inference resources cleaned up")
    
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.cleanup()
    
    def __del__(self):
        """Destructor."""
        self.cleanup()


# Convenience functions for quick usage
def create_mlm_inference(
    config_path: str,
    checkpoint_path: str,
    **kwargs
) -> MLMPipeline:
    """
    Create a standalone MLM inference pipeline.
    
    Args:
        config_path: Path to training config
        checkpoint_path: Path to checkpoint
        **kwargs: Additional inference config parameters
        
    Returns:
        MLM pipeline instance
    """
    model_config = ModelConfig(config_path)
    inference_config = InferenceConfig(**kwargs)
    
    return MLMPipeline(model_config, inference_config, checkpoint_path)


def create_embedding_inference(
    config_path: str,
    checkpoint_path: str,
    **kwargs
) -> EmbeddingPipeline:
    """
    Create a standalone embedding inference pipeline.
    
    Args:
        config_path: Path to training config
        checkpoint_path: Path to checkpoint
        **kwargs: Additional inference config parameters
        
    Returns:
        Embedding pipeline instance
    """
    model_config = ModelConfig(config_path)
    inference_config = InferenceConfig(**kwargs)
    
    return EmbeddingPipeline(model_config, inference_config, checkpoint_path) 