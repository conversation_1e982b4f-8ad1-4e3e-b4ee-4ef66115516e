#!/usr/bin/env python3
# Copyright 2024 onwards Answer.AI, LightOn, and contributors
# License: Apache-2.0

"""
Simple test script to validate the ModernBERT inference system components.
"""

import sys
import os
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_basic_imports():
    """Test basic imports."""
    print("Testing basic imports...")
    
    try:
        import torch
        print("✓ PyTorch imported")
        
        import numpy as np
        print("✓ NumPy imported")
        
        from transformers import AutoTokenizer
        print("✓ Transformers imported")
        
        from omegaconf import OmegaConf
        print("✓ OmegaConf imported")
        
        return True
    except ImportError as e:
        print(f"✗ Basic import failed: {e}")
        return False

def test_inference_imports():
    """Test inference module imports."""
    print("\nTesting inference imports...")
    
    try:
        # Test individual module imports
        from config.model_config import ModelConfig
        print("✓ ModelConfig imported")
        
        from config.inference_config import InferenceConfig
        print("✓ InferenceConfig imported")
        
        # Test main import
        from inference import ModernBERTInference
        print("✓ ModernBERTInference imported")
        
        return True
    except ImportError as e:
        print(f"✗ Inference import failed: {e}")
        return False

def test_config_loading():
    """Test configuration loading."""
    print("\nTesting configuration loading...")
    
    try:
        config_path = "yamls/main/flex-bert-modernbert-base-edu-fw-320B-paper-custom-tokenizer.yaml"
        
        if not os.path.exists(config_path):
            print(f"⚠ Config file not found at {config_path}")
            print("  This is expected - you'll need to update paths for actual usage")
            return True
        
        from config.model_config import ModelConfig
        model_config = ModelConfig(config_path)
        print(f"✓ Model config loaded successfully")
        print(f"  - Hidden size: {getattr(model_config, 'hidden_size', 'N/A')}")
        print(f"  - Padding: {getattr(model_config, 'padding', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"✗ Config loading failed: {e}")
        return False

def test_file_structure():
    """Test that all expected files are present."""
    print("\nTesting file structure...")
    
    required_files = [
        "inference/inference.py",
        "inference/config/__init__.py",
        "inference/config/model_config.py",
        "inference/config/inference_config.py",
        "inference/core/__init__.py",
        "inference/pipelines/__init__.py",
        "inference/utils/__init__.py",
        "inference/examples/mlm_example.py",
        "inference/examples/embedding_example.py",
        "inference/README.md"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"✗ Missing files: {missing_files}")
        return False
    else:
        print(f"✓ All {len(required_files)} required files present")
        return True

def main():
    """Run all tests."""
    print("=" * 60)
    print("ModernBERT Inference System Validation")
    print("=" * 60)
    
    # Environment info
    conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'unknown')
    print(f"Conda environment: {conda_env}")
    print(f"Python version: {sys.version}")
    print(f"Working directory: {os.getcwd()}")
    
    tests = [
        test_basic_imports,
        test_file_structure,
        test_inference_imports,
        test_config_loading,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n" + "=" * 60)
    print(f"Validation Results: {passed}/{total} tests passed")
    print("=" * 60)
    
    if passed == total:
        print("🎉 All validation tests passed!")
        print("\nThe inference system is properly installed and ready to use.")
        print("\nNext steps:")
        print("1. Update checkpoint paths in example scripts:")
        print("   - inference/examples/mlm_example.py") 
        print("   - inference/examples/embedding_example.py")
        print("2. Run an example: python inference/examples/mlm_example.py")
    else:
        print("⚠ Some validation tests failed.")
        print("Check the error messages above and ensure all dependencies are installed.")
        
    print(f"\nFor usage instructions, see: inference/README.md")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 