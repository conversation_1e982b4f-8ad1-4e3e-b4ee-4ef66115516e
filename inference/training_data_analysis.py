#!/usr/bin/env python3
"""
Training Data Batch Investigation Script

This script replicates the exact training data processing pipeline to understand
what the model was actually trained on and identify potential issues.
"""

import sys
import os
from pathlib import Path
import torch
import numpy as np
from transformers import AutoTokenizer, DataCollatorForLanguageModeling
from collections import Counter
import json

# Add project root to path
sys.path.insert(0, str(Path.cwd()))

def analyze_training_data_batches():
    """Analyze training data batches using exact training configuration."""
    
    print("🔍 Training Data Batch Investigation")
    print("=" * 60)
    
    # Load exact training configuration
    config_path = "yamls/main/flex-bert-modernbert-base-edu-fw-320B-paper-custom-tokenizer.yaml"
    tokenizer_path = "/s2_nfs/tokenizer_spm_50368_140B_EnFa_hf_edited"
    data_path = "/s2_nfs/mds_300BT_corpus/"
    
    # Training parameters from config
    max_seq_len = 1024
    mlm_probability = 0.3  # 30% masking during training
    eval_mlm_probability = 0.15  # 15% masking during evaluation
    
    print(f"Training Configuration:")
    print(f"  - Max sequence length: {max_seq_len}")
    print(f"  - MLM probability (training): {mlm_probability}")
    print(f"  - MLM probability (evaluation): {eval_mlm_probability}")
    print(f"  - Tokenizer: {tokenizer_path}")
    print(f"  - Data path: {data_path}")
    
    # Load tokenizer
    print(f"\n📚 Loading tokenizer...")
    try:
        tokenizer = AutoTokenizer.from_pretrained(tokenizer_path)
        print(f"  ✓ Tokenizer loaded: vocab_size={len(tokenizer)}")
        print(f"  ✓ Mask token: '{tokenizer.mask_token}' (ID: {tokenizer.mask_token_id})")
        print(f"  ✓ Pad token: '{tokenizer.pad_token}' (ID: {tokenizer.pad_token_id})")
    except Exception as e:
        print(f"  ❌ Failed to load tokenizer: {e}")
        return
    
    # Create MLM data collator (same as training)
    print(f"\n🎭 Creating MLM data collator...")
    collator_training = DataCollatorForLanguageModeling(
        tokenizer=tokenizer,
        mlm=True,
        mlm_probability=mlm_probability
    )
    
    collator_eval = DataCollatorForLanguageModeling(
        tokenizer=tokenizer,
        mlm=True,
        mlm_probability=eval_mlm_probability
    )
    
    # Test with sample texts (simulating training data)
    sample_texts = [
        "The capital of France is Paris, a beautiful city known for its art, culture, and history. The Eiffel Tower stands as an iconic symbol of the city.",
        "Machine learning is a subset of artificial intelligence that enables computers to learn and improve from experience without being explicitly programmed.",
        "Climate change refers to long-term shifts in global temperatures and weather patterns, primarily caused by human activities since the mid-20th century.",
        "The human brain contains approximately 86 billion neurons that communicate through electrical and chemical signals to process information and control behavior.",
        "Renewable energy sources such as solar, wind, and hydroelectric power are becoming increasingly important for sustainable development and environmental protection."
    ]
    
    print(f"\n📊 Analyzing MLM masking patterns...")
    
    # Analyze training masking (30%)
    print(f"\n--- TRAINING MASKING (30%) ---")
    analyze_masking_patterns(sample_texts, tokenizer, collator_training, "Training", max_seq_len)
    
    # Analyze evaluation masking (15%)
    print(f"\n--- EVALUATION MASKING (15%) ---")
    analyze_masking_patterns(sample_texts, tokenizer, collator_eval, "Evaluation", max_seq_len)
    
    # Test with our problematic example
    print(f"\n--- TESTING OUR PROBLEMATIC EXAMPLE ---")
    test_problematic_example(tokenizer, collator_training, collator_eval)

def analyze_masking_patterns(texts, tokenizer, collator, mode_name, max_seq_len):
    """Analyze what types of tokens get masked during training."""
    
    all_masked_tokens = []
    all_original_tokens = []
    mask_positions_stats = []
    
    for i, text in enumerate(texts):
        print(f"\n  Text {i+1}: {text[:100]}...")
        
        # Tokenize
        encoded = tokenizer(
            text,
            truncation=True,
            max_length=max_seq_len,
            padding='max_length',
            return_tensors='pt'
        )
        
        # Apply MLM masking
        batch = collator([{
            'input_ids': encoded['input_ids'].squeeze(),
            'attention_mask': encoded['attention_mask'].squeeze()
        }])
        
        input_ids = batch['input_ids'].squeeze()
        labels = batch['labels'].squeeze()
        
        # Find masked positions
        mask_positions = (input_ids == tokenizer.mask_token_id).nonzero(as_tuple=False).flatten()
        masked_token_positions = (labels != -100).nonzero(as_tuple=False).flatten()
        
        print(f"    Sequence length: {(encoded['attention_mask'].squeeze() == 1).sum().item()}")
        print(f"    Masked positions: {len(masked_token_positions)} ({len(masked_token_positions)/max_seq_len*100:.1f}%)")
        print(f"    Explicit [MASK] tokens: {len(mask_positions)}")
        
        # Analyze what got masked
        for pos in masked_token_positions[:10]:  # Show first 10
            original_token_id = labels[pos].item()
            masked_token_id = input_ids[pos].item()
            
            original_token = tokenizer.decode([original_token_id])
            if masked_token_id == tokenizer.mask_token_id:
                masked_token = "[MASK]"
            else:
                masked_token = tokenizer.decode([masked_token_id])
            
            all_original_tokens.append(original_token)
            all_masked_tokens.append(masked_token)
            
            print(f"      Pos {pos}: '{original_token}' -> '{masked_token}'")
        
        mask_positions_stats.append(len(masked_token_positions))
    
    # Summary statistics
    print(f"\n  {mode_name} Summary:")
    print(f"    Average masked tokens per sequence: {np.mean(mask_positions_stats):.1f}")
    print(f"    Masking rate: {np.mean(mask_positions_stats)/max_seq_len*100:.1f}%")
    
    # Token type analysis
    if all_original_tokens:
        token_counter = Counter(all_original_tokens)
        print(f"    Most commonly masked tokens:")
        for token, count in token_counter.most_common(10):
            print(f"      '{token}': {count} times")

def test_problematic_example(tokenizer, collator_training, collator_eval):
    """Test our problematic example under training conditions."""
    
    # Our problematic short example
    short_text = "The capital of France is Paris."
    
    # Create a longer context (simulating training data)
    long_text = """The capital of France is Paris, one of the most beautiful and culturally rich cities in the world. 
    Paris is known for its iconic landmarks such as the Eiffel Tower, Notre-Dame Cathedral, and the Louvre Museum. 
    The city has been a center of art, fashion, and intellectual thought for centuries. Many famous writers, artists, 
    and philosophers have called Paris home, contributing to its reputation as the "City of Light". The Seine River 
    flows through the heart of the city, adding to its romantic charm and providing scenic views from its many bridges."""
    
    print(f"  Short text: {short_text}")
    print(f"  Long text: {long_text[:100]}...")
    
    for text, name in [(short_text, "Short"), (long_text, "Long")]:
        print(f"\n  --- {name} Text Analysis ---")
        
        # Tokenize
        encoded = tokenizer(
            text,
            truncation=True,
            max_length=1024,
            padding='max_length',
            return_tensors='pt'
        )
        
        # Test with training masking (30%)
        batch_train = collator_training([{
            'input_ids': encoded['input_ids'].squeeze(),
            'attention_mask': encoded['attention_mask'].squeeze()
        }])
        
        # Test with eval masking (15%)
        batch_eval = collator_eval([{
            'input_ids': encoded['input_ids'].squeeze(),
            'attention_mask': encoded['attention_mask'].squeeze()
        }])
        
        for batch, mode in [(batch_train, "Training(30%)"), (batch_eval, "Eval(15%)")]:
            input_ids = batch['input_ids'].squeeze()
            labels = batch['labels'].squeeze()
            
            # Find where "Paris" would be
            paris_token_id = tokenizer.encode("Paris", add_special_tokens=False)[0]
            masked_positions = (labels != -100).nonzero(as_tuple=False).flatten()
            
            print(f"    {mode}: {len(masked_positions)} tokens masked")
            
            # Check if Paris gets masked
            original_tokens = tokenizer.tokenize(text)
            if "Paris" in text:
                print(f"      Paris token ID: {paris_token_id}")
                paris_positions = (labels == paris_token_id).nonzero(as_tuple=False).flatten()
                if len(paris_positions) > 0:
                    print(f"      Paris is masked at positions: {paris_positions.tolist()}")
                else:
                    print(f"      Paris is not masked in this example")

def analyze_actual_training_data():
    """Try to load and analyze actual training data if available."""

    print(f"\n🗂️ Attempting to analyze actual training data...")

    data_path = "/s2_nfs/mds_300BT_corpus/"
    tokenizer_path = "/s2_nfs/tokenizer_spm_50368_140B_EnFa_hf_edited"

    try:
        # Check if training data exists
        if not os.path.exists(data_path):
            print(f"  ❌ Training data path not accessible: {data_path}")
            return

        print(f"  ✓ Training data path exists: {data_path}")

        # List contents
        contents = os.listdir(data_path)
        print(f"  📁 Contents: {contents[:10]}...")  # Show first 10 items

        # Look for train split
        train_path = os.path.join(data_path, "train")
        if os.path.exists(train_path):
            print(f"  ✓ Train split found: {train_path}")
            train_contents = os.listdir(train_path)
            print(f"  📁 Train contents: {train_contents[:5]}...")
        else:
            print(f"  ❌ No train split found")

    except Exception as e:
        print(f"  ❌ Error accessing training data: {e}")

def comprehensive_mlm_test():
    """Test MLM under various conditions to understand the discrepancy."""

    print(f"\n🧪 Comprehensive MLM Testing")
    print("=" * 60)

    tokenizer_path = "/s2_nfs/tokenizer_spm_50368_140B_EnFa_hf_edited"
    tokenizer = AutoTokenizer.from_pretrained(tokenizer_path)

    # Test scenarios
    test_scenarios = [
        {
            "name": "Short sentence (our original test)",
            "text": "The capital of France is Paris.",
            "expected": "Paris"
        },
        {
            "name": "Medium context",
            "text": "France is a beautiful country in Europe. The capital of France is Paris. Paris is known for its culture.",
            "expected": "Paris"
        },
        {
            "name": "Long context (training-like)",
            "text": """France, officially the French Republic, is a country located primarily in Western Europe.
            It consists of metropolitan France and several overseas regions and territories.
            The capital of France is Paris, which is also the country's largest city and economic center.
            Paris is renowned worldwide for its art, fashion, gastronomy, and culture. The city houses many
            famous landmarks including the Eiffel Tower, Notre-Dame Cathedral, and the Louvre Museum.""",
            "expected": "Paris"
        }
    ]

    # Test with different masking rates
    masking_rates = [0.15, 0.3]  # Evaluation and training rates

    for scenario in test_scenarios:
        print(f"\n--- {scenario['name']} ---")
        print(f"Text: {scenario['text'][:100]}...")

        for rate in masking_rates:
            print(f"\n  Masking rate: {rate*100}%")

            collator = DataCollatorForLanguageModeling(
                tokenizer=tokenizer,
                mlm=True,
                mlm_probability=rate
            )

            # Run multiple trials to see variability
            paris_masked_count = 0
            total_trials = 10

            for trial in range(total_trials):
                encoded = tokenizer(
                    scenario['text'],
                    truncation=True,
                    max_length=1024,
                    padding='max_length',
                    return_tensors='pt'
                )

                batch = collator([{
                    'input_ids': encoded['input_ids'].squeeze(),
                    'attention_mask': encoded['attention_mask'].squeeze()
                }])

                labels = batch['labels'].squeeze()

                # Check if Paris gets masked
                paris_token_id = tokenizer.encode("Paris", add_special_tokens=False)[0]
                if paris_token_id in labels:
                    paris_masked_count += 1

            print(f"    Paris masked in {paris_masked_count}/{total_trials} trials ({paris_masked_count/total_trials*100:.1f}%)")

if __name__ == "__main__":
    analyze_training_data_batches()
    analyze_actual_training_data()
    comprehensive_mlm_test()
