# Copyright 2024 onwards Answer.AI, LightOn, and contributors
# License: Apache-2.0

import os
import sys
from pathlib import Path
from typing import Dict, Any, Optional, Union, Tuple
import torch
import logging

# Add project root to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

logger = logging.getLogger(__name__)


class CheckpointManager:
    """
    Handles loading and validating model checkpoints from various formats.
    
    Supports:
    - Composer checkpoint format (.pt files)
    - Standard PyTorch state dict format
    - Automatic format detection
    """
    
    def __init__(self, checkpoint_path: str):
        """
        Initialize CheckpointManager.
        
        Args:
            checkpoint_path: Path to the checkpoint file
        """
        self.checkpoint_path = Path(checkpoint_path)
        if not self.checkpoint_path.exists():
            raise FileNotFoundError(f"Checkpoint file not found: {checkpoint_path}")
        
        self.checkpoint_data = None
        self.format_type = None
        
    def load_checkpoint(self, map_location: str = "cpu") -> <PERSON><PERSON>[Dict[str, Any], str]:
        """
        Load checkpoint and determine its format.
        
        Args:
            map_location: Device to load checkpoint to
            
        Returns:
            Tuple of (state_dict, format_type)
        """
        logger.info(f"Loading checkpoint from {self.checkpoint_path}")
        
        try:
            self.checkpoint_data = torch.load(self.checkpoint_path, map_location=map_location)
        except Exception as e:
            raise RuntimeError(f"Failed to load checkpoint: {e}")
        
        # Determine checkpoint format and extract state dict
        state_dict, self.format_type = self._extract_state_dict()
        
        logger.info(f"Loaded checkpoint in {self.format_type} format")
        return state_dict, self.format_type
    
    def _extract_state_dict(self) -> Tuple[Dict[str, Any], str]:
        """
        Extract model state dict from checkpoint data.
        
        Returns:
            Tuple of (state_dict, format_type)
        """
        if self.checkpoint_data is None:
            raise RuntimeError("Checkpoint not loaded. Call load_checkpoint() first.")
        
        # Check for Composer format
        if isinstance(self.checkpoint_data, dict) and "state" in self.checkpoint_data:
            logger.debug("Detected Composer checkpoint format")
            state = self.checkpoint_data["state"]
            
            if "model" in state:
                return state["model"], "composer"
            else:
                raise ValueError("Composer checkpoint missing 'model' in state")
        
        # Check for standard PyTorch format with model wrapper
        elif isinstance(self.checkpoint_data, dict) and "model" in self.checkpoint_data:
            logger.debug("Detected PyTorch checkpoint with model wrapper")
            return self.checkpoint_data["model"], "pytorch_wrapped"
        
        # Check for direct state dict
        elif isinstance(self.checkpoint_data, dict) and self._is_state_dict(self.checkpoint_data):
            logger.debug("Detected direct state dict format")
            return self.checkpoint_data, "direct_state_dict"
        
        else:
            raise ValueError(f"Unrecognized checkpoint format. Available keys: {list(self.checkpoint_data.keys())}")
    
    def _is_state_dict(self, data: Dict[str, Any]) -> bool:
        """
        Check if data looks like a model state dict.
        
        Args:
            data: Dictionary to check
            
        Returns:
            True if data appears to be a state dict
        """
        # Look for common model parameter patterns
        if not isinstance(data, dict):
            return False
        
        # Check for typical model parameter names
        state_dict_patterns = [
            "bert.embeddings",
            "bert.encoder",
            "embeddings.tok_embeddings", 
            "encoder.layers",
            "head.",
            "decoder.weight",
            "decoder.bias"
        ]
        
        keys = list(data.keys())
        return any(any(pattern in key for pattern in state_dict_patterns) for key in keys)
    
    def get_checkpoint_info(self) -> Dict[str, Any]:
        """
        Get metadata about the loaded checkpoint.
        
        Returns:
            Dictionary with checkpoint information
        """
        if self.checkpoint_data is None:
            raise RuntimeError("Checkpoint not loaded. Call load_checkpoint() first.")
        
        info = {
            "path": str(self.checkpoint_path),
            "format": self.format_type,
            "file_size_mb": self.checkpoint_path.stat().st_size / (1024 * 1024),
        }
        
        # Add format-specific information
        if self.format_type == "composer":
            state = self.checkpoint_data.get("state", {})
            info.update({
                "timestamp": self.checkpoint_data.get("timestamp"),
                "run_name": self.checkpoint_data.get("run_name"),
                "epoch": state.get("timestamp", {}).get("epoch"),
                "batch": state.get("timestamp", {}).get("batch"),
                "optimizers": list(state.get("optimizers", {}).keys()),
                "algorithms": list(state.get("algorithms", {}).keys()),
            })
        
        return info
    
    def validate_state_dict(self, state_dict: Dict[str, Any], expected_keys: Optional[set] = None) -> bool:
        """
        Validate that state dict contains expected parameters.
        
        Args:
            state_dict: Model state dict to validate
            expected_keys: Optional set of expected parameter names
            
        Returns:
            True if validation passes
        """
        if not isinstance(state_dict, dict):
            raise ValueError("State dict must be a dictionary")
        
        if len(state_dict) == 0:
            raise ValueError("State dict is empty")
        
        # Basic validation
        param_count = sum(param.numel() for param in state_dict.values() if torch.is_tensor(param))
        logger.info(f"State dict contains {len(state_dict)} parameters totaling {param_count:,} values")
        
        # Check for expected keys if provided
        if expected_keys is not None:
            missing_keys = expected_keys - set(state_dict.keys())
            unexpected_keys = set(state_dict.keys()) - expected_keys
            
            if missing_keys:
                logger.warning(f"Missing keys in state dict: {missing_keys}")
            if unexpected_keys:
                logger.warning(f"Unexpected keys in state dict: {unexpected_keys}")
        
        return True
    
    def clean_state_dict_keys(self, state_dict: Dict[str, Any], prefix_to_remove: str = "model.") -> Dict[str, Any]:
        """
        Clean state dict keys by removing common prefixes.
        
        Args:
            state_dict: Original state dict
            prefix_to_remove: Prefix to remove from keys
            
        Returns:
            Cleaned state dict
        """
        if not prefix_to_remove:
            return state_dict
        
        cleaned_dict = {}
        for key, value in state_dict.items():
            if key.startswith(prefix_to_remove):
                new_key = key[len(prefix_to_remove):]
                cleaned_dict[new_key] = value
            else:
                cleaned_dict[key] = value
        
        logger.debug(f"Cleaned {len([k for k in state_dict.keys() if k.startswith(prefix_to_remove)])} keys")
        return cleaned_dict 