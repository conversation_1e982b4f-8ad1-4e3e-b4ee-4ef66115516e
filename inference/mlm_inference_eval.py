#!/usr/bin/env python3
"""
Comprehensive MLM Inference Evaluation Script

This script replicates the exact training evaluation setup to validate our cleaned
MLM inference pipeline against the training baseline (~78% accuracy).

Usage:
    conda activate bert24
    python mlm_inference_eval.py
"""

import os
import sys
import torch
import numpy as np
import argparse
from pathlib import Path
from typing import Dict, List, Tuple, Any
from tqdm import tqdm
from collections import defaultdict
import time
import random

# Add project root to path
sys.path.insert(0, str(Path.cwd()))

# Import training infrastructure
from omegaconf import OmegaConf
from transformers import AutoTokenizer, DataCollatorForLanguageModeling
from torch.utils.data import DataLoader

# Import our cleaned inference pipeline
from inference import ModernBERTInference

# Import training data infrastructure
from src.text_data import build_no_streaming_dataset

# Configuration paths
CONFIG_PATH = "yamls/main/flex-bert-modernbert-base-edu-fw-320B-paper-custom-tokenizer.yaml"
CHECKPOINT_PATH = "/s2_nfs/ckpt/flex-bert-modernbert-base-edu-fw-320B-article-custom-tokenizer/ep3-ba339282-rank0.pt"

def parse_arguments():
    """Parse command-line arguments for evaluation configuration."""
    parser = argparse.ArgumentParser(
        description="MLM Inference Pipeline Evaluation Script",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    # Evaluation parameters
    parser.add_argument(
        "--global_eval_batch_size",
        type=int,
        default=512,
        help="Global evaluation batch size (total across all devices)"
    )

    parser.add_argument(
        "--device_eval_microbatch_size",
        type=int,
        default=64,
        help="Device-level microbatch size for evaluation"
    )

    parser.add_argument(
        "--eval_subset_num_batches",
        type=int,
        default=64,
        help="Number of batches to evaluate (use -1 for full dataset)"
    )

    parser.add_argument(
        "--eval_mlm_probability",
        type=float,
        default=0.15,
        help="MLM masking probability for evaluation (should match training validation)"
    )

    parser.add_argument(
        "--max_seq_len",
        type=int,
        default=1024,
        help="Maximum sequence length (should match training config)"
    )

    parser.add_argument(
        "--eval_seed",
        type=int,
        default=17,
        help="Random seed for reproducible evaluation"
    )

    # Optional configuration overrides
    parser.add_argument(
        "--config_path",
        type=str,
        default=CONFIG_PATH,
        help="Path to training configuration YAML file"
    )

    parser.add_argument(
        "--checkpoint_path",
        type=str,
        default=CHECKPOINT_PATH,
        help="Path to model checkpoint file"
    )

    parser.add_argument(
        "--device",
        type=str,
        default="auto",
        help="Device to use for inference (auto, cpu, cuda, cuda:0, etc.)"
    )

    parser.add_argument(
        "--precision",
        type=str,
        default="fp32",
        choices=["fp32", "fp16", "bf16"],
        help="Inference precision"
    )

    return parser.parse_args()

def load_config(config_path: str) -> Dict[str, Any]:
    """Load and resolve configuration like training does."""
    config_path = Path(config_path)
    with open(config_path) as f:
        config = OmegaConf.load(f)
    
    # Load defaults if they exist
    defaults_path = config_path.parent.parent / "defaults.yaml"
    if defaults_path.exists():
        with open(defaults_path) as f:
            default_config = OmegaConf.load(f)
        config = OmegaConf.merge(default_config, config)
    
    OmegaConf.resolve(config)
    return OmegaConf.to_container(config, resolve=True)

def create_evaluation_dataloader(config: Dict[str, Any], tokenizer, args) -> DataLoader:
    """Create evaluation dataloader matching training setup exactly."""
    print("📊 Setting up evaluation dataloader...")
    
    # Create eval loader config matching training
    eval_cfg = OmegaConf.create({
        'name': 'text',
        'dataset': {
            'streaming': False,
            'local': config['data_local'],
            'remote': config.get('data_remote', None),
            'split': 'test',  # Use test split as specified in config
            'tokenizer_name': config['tokenizer_name'],
            'max_seq_len': args.max_seq_len,
            'shuffle': False,  # No shuffle for evaluation
            'mlm_probability': args.eval_mlm_probability
        },
        'drop_last': True,  # Match training config
        'num_workers': 4,  # Enable multiprocessing for better performance
        'pin_memory': True,
        'prefetch_factor': 2,
        'persistent_workers': True,  # Keep workers alive for better performance
        'timeout': 0
    })
    
    # Build dataset using training infrastructure
    # Note: Model was trained with unpadded configuration, but our inference pipeline
    # is designed to handle padded inputs efficiently, so we use pad_sequences=True
    dataset = build_no_streaming_dataset(
        eval_cfg,
        tokenizer=tokenizer,
        pad_sequences=True  # Use padded sequences for our cleaned pipeline
    )
    
    # Create MLM collator with exact evaluation parameters
    collate_fn = DataCollatorForLanguageModeling(
        tokenizer=tokenizer,
        mlm=True,
        mlm_probability=args.eval_mlm_probability
    )

    # Create dataloader with training-matching parameters
    dataloader = DataLoader(
        dataset,
        collate_fn=collate_fn,
        batch_size=args.device_eval_microbatch_size,  # Use microbatch size for memory efficiency
        drop_last=True,
        num_workers=eval_cfg.num_workers,
        pin_memory=eval_cfg.pin_memory,
        prefetch_factor=eval_cfg.prefetch_factor,
        persistent_workers=eval_cfg.persistent_workers,
        timeout=eval_cfg.timeout,
        shuffle=False  # Deterministic evaluation
    )
    
    print(f"  ✓ Dataset: {len(dataset)} samples")
    print(f"  ✓ Batch size: {args.device_eval_microbatch_size}")
    print(f"  ✓ MLM probability: {args.eval_mlm_probability}")
    print(f"  ✓ Max sequence length: {args.max_seq_len}")
    
    return dataloader

def evaluate_mlm_accuracy(inference_system, dataloader, tokenizer, args) -> Dict[str, float]:
    """Evaluate MLM accuracy using our cleaned inference pipeline."""
    print("\n🎯 Running MLM accuracy evaluation...")
    
    total_masked_tokens = 0
    correct_top1_predictions = 0
    correct_top5_predictions = 0
    processed_batches = 0

    # Track accuracy by sequence length for analysis
    accuracy_by_length = defaultdict(lambda: {"total": 0, "correct": 0})

    # Enhanced statistics tracking for debugging
    batch_stats = {
        'total_tokens': 0,
        'non_pad_tokens': 0,
        'maskable_tokens': 0,
        'actual_masks': 0,
        'sequences_processed': 0,
        'expected_masks_15pct': 0,
        'expected_masks_actual_rate': 0
    }

    # Progress tracking
    start_time = time.time()
    
    # Determine number of batches to process
    max_batches = len(dataloader) if args.eval_subset_num_batches == -1 else args.eval_subset_num_batches

    with tqdm(total=min(max_batches, len(dataloader)),
              desc="Evaluating", unit="batch") as pbar:

        for batch_idx, batch in enumerate(dataloader):
            if args.eval_subset_num_batches != -1 and batch_idx >= args.eval_subset_num_batches:
                break
                
            try:
                batch_accuracy = evaluate_batch(
                    inference_system, batch, tokenizer, accuracy_by_length
                )

                # Collect batch statistics for analysis
                input_ids = batch["input_ids"]
                labels = batch["labels"]

                batch_total_tokens = input_ids.numel()
                batch_non_pad = (input_ids != tokenizer.pad_token_id).sum().item() if hasattr(tokenizer, 'pad_token_id') else batch_total_tokens
                batch_maskable = (labels != -100).sum().item()
                batch_sequences = input_ids.shape[0]

                batch_stats['total_tokens'] += batch_total_tokens
                batch_stats['non_pad_tokens'] += batch_non_pad
                batch_stats['maskable_tokens'] += batch_maskable
                batch_stats['actual_masks'] += batch_accuracy["total_masks"]
                batch_stats['sequences_processed'] += batch_sequences
                batch_stats['expected_masks_15pct'] += int(batch_non_pad * 0.15)
                batch_stats['expected_masks_actual_rate'] += int(batch_non_pad * args.eval_mlm_probability)

                total_masked_tokens += batch_accuracy["total_masks"]
                correct_top1_predictions += batch_accuracy["correct_top1"]
                correct_top5_predictions += batch_accuracy["correct_top5"]
                processed_batches += 1
                
                # Update progress
                if total_masked_tokens > 0:
                    current_accuracy = correct_top1_predictions / total_masked_tokens
                    pbar.set_postfix({
                        'Top-1 Acc': f'{current_accuracy:.3f}',
                        'Masks': total_masked_tokens
                    })
                
                pbar.update(1)
                
            except Exception as e:
                print(f"    ⚠️  Error in batch {batch_idx}: {e}")
                continue
    
    # Calculate final metrics
    elapsed_time = time.time() - start_time
    
    results = {
        "total_batches_processed": processed_batches,
        "total_masked_tokens": total_masked_tokens,
        "correct_top1_predictions": correct_top1_predictions,
        "correct_top5_predictions": correct_top5_predictions,
        "top1_accuracy": correct_top1_predictions / total_masked_tokens if total_masked_tokens > 0 else 0.0,
        "top5_accuracy": correct_top5_predictions / total_masked_tokens if total_masked_tokens > 0 else 0.0,
        "evaluation_time_seconds": elapsed_time,
        "tokens_per_second": total_masked_tokens / elapsed_time if elapsed_time > 0 else 0.0
    }
    
    return results

def evaluate_batch(inference_system, batch, tokenizer, accuracy_by_length) -> Dict[str, int]:
    """Evaluate a single batch and return accuracy metrics."""
    input_ids = batch["input_ids"]
    labels = batch["labels"]

    batch_total_masks = 0
    batch_correct_top1 = 0
    batch_correct_top5 = 0

    # Debug: Print batch info for first few batches
    if hasattr(evaluate_batch, 'call_count'):
        evaluate_batch.call_count += 1
    else:
        evaluate_batch.call_count = 1

    # Enhanced debugging with token analysis
    if evaluate_batch.call_count <= 3:  # Debug first 3 batches
        print(f"    Debug batch {evaluate_batch.call_count}: input_ids shape={input_ids.shape}, "
              f"labels shape={labels.shape}")

        # Analyze token distribution
        total_tokens = input_ids.numel()
        non_pad_tokens = (input_ids != tokenizer.pad_token_id).sum().item() if hasattr(tokenizer, 'pad_token_id') else total_tokens
        maskable_positions = (labels != -100).sum().item()

        print(f"    Token analysis: total={total_tokens}, non_pad={non_pad_tokens}, "
              f"maskable={maskable_positions}, mask_rate={maskable_positions/non_pad_tokens:.3f}")

        # Sample sequence analysis
        seq_0_length = (input_ids[0] != tokenizer.pad_token_id).sum().item() if hasattr(tokenizer, 'pad_token_id') else len(input_ids[0])
        seq_0_masks = (labels[0] != -100).sum().item()
        print(f"    Sequence 0: length={seq_0_length}, masks={seq_0_masks}, "
              f"effective_rate={seq_0_masks/seq_0_length:.3f}")

    # Process each sequence in the batch
    for seq_idx in range(input_ids.shape[0]):
        seq_input_ids = input_ids[seq_idx]
        seq_labels = labels[seq_idx]

        # Find masked positions (where labels != -100)
        masked_positions = (seq_labels != -100).nonzero(as_tuple=False).flatten()

        if len(masked_positions) == 0:
            continue

        # Debug: Print masking info for first sequence of first batch
        if evaluate_batch.call_count == 1 and seq_idx == 0:
            print(f"    Debug seq 0: {len(masked_positions)} masked positions out of {len(seq_input_ids)} tokens")
            print(f"    Masked positions: {masked_positions[:10].tolist()}...")  # First 10 positions

        # Convert to text with <mask> tokens for our inference pipeline
        masked_text = create_masked_text(seq_input_ids, masked_positions, tokenizer)

        try:
            # Get predictions from our cleaned inference pipeline
            predictions = inference_system.predict_masked_tokens(
                masked_text,
                top_k=5,  # Get top-5 for both top-1 and top-5 accuracy
                top_p=1.0,  # No top-p filtering for evaluation
                temperature=1.0  # No temperature scaling for evaluation
            )

            # Debug: Print prediction info for first sequence of first batch
            if evaluate_batch.call_count == 1 and seq_idx == 0:
                if predictions and "mask_predictions" in predictions:
                    print(f"    Debug predictions: {len(predictions['mask_predictions'])} predictions returned")
                    if predictions['mask_predictions']:
                        first_pred = predictions['mask_predictions'][0]
                        print(f"    First prediction: {first_pred}")
                else:
                    print(f"    Debug: No predictions returned or wrong format: {predictions}")

            # Extract and evaluate predictions
            seq_accuracy = evaluate_sequence_predictions(
                predictions, seq_labels, masked_positions, seq_input_ids.shape[0], accuracy_by_length
            )

            batch_total_masks += seq_accuracy["total_masks"]
            batch_correct_top1 += seq_accuracy["correct_top1"]
            batch_correct_top5 += seq_accuracy["correct_top5"]

        except Exception as e:
            print(f"      Error processing sequence {seq_idx}: {e}")
            continue

    return {
        "total_masks": batch_total_masks,
        "correct_top1": batch_correct_top1,
        "correct_top5": batch_correct_top5
    }

def create_masked_text(input_ids: torch.Tensor, masked_positions: torch.Tensor, tokenizer) -> str:
    """Convert input_ids with masked positions to text with <mask> tokens."""
    # Create a copy and replace masked positions with mask token
    masked_input_ids = input_ids.clone()
    masked_input_ids[masked_positions] = tokenizer.mask_token_id
    
    # Decode to text
    text = tokenizer.decode(masked_input_ids, skip_special_tokens=False)
    return text

def evaluate_sequence_predictions(
    predictions: Dict[str, Any],
    labels: torch.Tensor,
    masked_positions: torch.Tensor,
    seq_len: int,
    accuracy_by_length: Dict
) -> Dict[str, int]:
    """Evaluate predictions for a single sequence."""

    if not predictions or "mask_predictions" not in predictions:
        return {"total_masks": len(masked_positions), "correct_top1": 0, "correct_top5": 0}

    mask_predictions = predictions["mask_predictions"]
    total_masks = len(masked_positions)
    correct_top1 = 0
    correct_top5 = 0

    # Debug: Track first sequence evaluation details
    if not hasattr(evaluate_sequence_predictions, 'call_count'):
        evaluate_sequence_predictions.call_count = 0
    debug_first_seq = evaluate_sequence_predictions.call_count == 0
    evaluate_sequence_predictions.call_count += 1

    # Match predictions to ground truth labels
    for i, mask_pos in enumerate(masked_positions):
        if i >= len(mask_predictions):
            if debug_first_seq:
                print(f"    Debug: Prediction mismatch - {i} >= {len(mask_predictions)} predictions")
            break

        ground_truth_token_id = labels[mask_pos].item()
        pred_list = mask_predictions[i]["predictions"]

        if not pred_list:
            if debug_first_seq:
                print(f"    Debug: Empty prediction list for position {i}")
            continue

        # Debug first few predictions
        if debug_first_seq and i < 3:
            print(f"    Debug mask {i}: ground_truth={ground_truth_token_id}, "
                  f"top_pred={pred_list[0]['token_id']}, match={pred_list[0]['token_id'] == ground_truth_token_id}")
            print(f"      Top 3 predictions: {[p['token_id'] for p in pred_list[:3]]}")

        # Check top-1 accuracy
        if pred_list[0]["token_id"] == ground_truth_token_id:
            correct_top1 += 1

        # Check top-5 accuracy
        top5_token_ids = [pred["token_id"] for pred in pred_list[:5]]
        if ground_truth_token_id in top5_token_ids:
            correct_top5 += 1

    # Track accuracy by sequence length
    length_bucket = (seq_len // 128) * 128  # Group by 128-token buckets
    accuracy_by_length[length_bucket]["total"] += total_masks
    accuracy_by_length[length_bucket]["correct"] += correct_top1

    return {
        "total_masks": total_masks,
        "correct_top1": correct_top1,
        "correct_top5": correct_top5
    }

def print_evaluation_results(results: Dict[str, float], accuracy_by_length: Dict):
    """Print comprehensive evaluation results."""
    print("\n" + "=" * 80)
    print("🎯 MLM INFERENCE EVALUATION RESULTS")
    print("=" * 80)

    # Main accuracy metrics
    print(f"📊 Overall Performance:")
    print(f"  • Total batches processed: {results['total_batches_processed']:,}")
    print(f"  • Total masked tokens: {results['total_masked_tokens']:,}")
    print(f"  • Top-1 accuracy: {results['top1_accuracy']:.4f} ({results['top1_accuracy']*100:.2f}%)")
    print(f"  • Top-5 accuracy: {results['top5_accuracy']:.4f} ({results['top5_accuracy']*100:.2f}%)")

    # Performance metrics
    print(f"\n⚡ Performance:")
    print(f"  • Evaluation time: {results['evaluation_time_seconds']:.2f} seconds")
    print(f"  • Tokens per second: {results['tokens_per_second']:.1f}")

    # Comparison with training baseline
    training_baseline = 0.78  # ~78% from training
    accuracy_diff = results['top1_accuracy'] - training_baseline

    print(f"\n📈 Comparison with Training Baseline:")
    print(f"  • Training baseline: {training_baseline:.4f} ({training_baseline*100:.2f}%)")
    print(f"  • Inference accuracy: {results['top1_accuracy']:.4f} ({results['top1_accuracy']*100:.2f}%)")
    print(f"  • Difference: {accuracy_diff:+.4f} ({accuracy_diff*100:+.2f}%)")

    if abs(accuracy_diff) < 0.02:  # Within 2%
        print("  ✅ EXCELLENT: Inference accuracy matches training baseline!")
    elif abs(accuracy_diff) < 0.05:  # Within 5%
        print("  ✅ GOOD: Inference accuracy is close to training baseline")
    else:
        print("  ⚠️  WARNING: Significant deviation from training baseline")

    # Accuracy by sequence length
    if accuracy_by_length:
        print(f"\n📏 Accuracy by Sequence Length:")
        for length_bucket in sorted(accuracy_by_length.keys()):
            stats = accuracy_by_length[length_bucket]
            if stats["total"] > 0:
                acc = stats["correct"] / stats["total"]
                print(f"  • {length_bucket:4d}-{length_bucket+127:4d} tokens: "
                      f"{acc:.4f} ({acc*100:.2f}%) [{stats['total']:,} masks]")

def main():
    """Main evaluation function."""
    # Parse command-line arguments
    args = parse_arguments()

    print("🚀 MLM Inference Pipeline Evaluation")
    print("=" * 80)
    print(f"Config: {args.config_path}")
    print(f"Checkpoint: {args.checkpoint_path}")
    print(f"Target accuracy: ~78% (training baseline)")
    print(f"Evaluation batches: {args.eval_subset_num_batches if args.eval_subset_num_batches != -1 else 'ALL'}")
    print(f"Batch size: {args.device_eval_microbatch_size}")
    print(f"Device: {args.device}")
    print(f"Precision: {args.precision}")

    # Set seeds for reproducibility
    torch.manual_seed(args.eval_seed)
    np.random.seed(args.eval_seed)
    random.seed(args.eval_seed)

    try:
        # Load configuration
        print("\n📋 Loading configuration...")
        config = load_config(args.config_path)
        tokenizer_path = config['tokenizer_name']

        # Load tokenizer
        print(f"🔤 Loading tokenizer: {tokenizer_path}")
        tokenizer = AutoTokenizer.from_pretrained(
            tokenizer_path,
            use_fast=True,
            trust_remote_code=True
        )
        print(f"  ✓ Vocab size: {len(tokenizer):,}")
        print(f"  ✓ Mask token: {tokenizer.mask_token}")

        # Create evaluation dataloader
        dataloader = create_evaluation_dataloader(config, tokenizer, args)

        # Initialize inference system
        print(f"\n🤖 Initializing inference system...")
        with ModernBERTInference(
            config_path=args.config_path,
            checkpoint_path=args.checkpoint_path,
            device=args.device,
            precision=args.precision
        ) as inference:
            print("  ✓ Inference system ready")

            # Track accuracy by length
            accuracy_by_length = defaultdict(lambda: {"total": 0, "correct": 0})

            # Run evaluation
            results = evaluate_mlm_accuracy(inference, dataloader, tokenizer, args)

            # Print results
            print_evaluation_results(results, accuracy_by_length)

    except Exception as e:
        print(f"\n❌ Evaluation failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

    print(f"\n✅ Evaluation completed successfully!")
    return 0

if __name__ == "__main__":
    exit(main())
