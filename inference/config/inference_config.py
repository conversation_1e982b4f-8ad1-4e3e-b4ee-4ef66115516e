# Copyright 2024 onwards Answer.AI, LightOn, and contributors
# License: Apache-2.0

from typing import Dict, Any, Optional, Union, Literal
from dataclasses import dataclass
import torch
import logging

logger = logging.getLogger(__name__)


@dataclass
class InferenceConfig:
    """
    Configuration for inference-specific settings.
    
    These settings control how inference is performed but don't affect
    the model architecture itself.
    """
    
    # Device and precision settings
    device: str = "auto"  # "auto", "cpu", "cuda", "cuda:0", etc.
    precision: Literal["fp32", "fp16", "bf16"] = "fp32"
    
    # Batch processing settings
    max_batch_size: int = 32
    max_sequence_length: int = 512
    
    # Model behavior settings
    use_cache: bool = False  # Whether to use KV caching (typically for generation)
    
    # MLM-specific settings
    mlm_top_k: int = 10
    mlm_top_p: float = 0.9
    mlm_temperature: float = 1.0
    mlm_return_all_predictions: bool = False
    
    # Embedding-specific settings
    embedding_pooling_strategy: Literal["mean", "cls", "max", "mean_sqrt_len"] = "mean"
    embedding_normalize: bool = True
    embedding_layer: int = -1  # -1 for last layer, 0-based indexing
    
    # Performance settings
    use_torch_compile: bool = False
    enable_memory_efficient_attention: bool = True
    gradient_checkpointing: bool = False
    
    # Output settings
    return_attention_weights: bool = False
    return_hidden_states: bool = False
    
    def __post_init__(self):
        """Validate and normalize configuration after initialization."""
        self._validate_device()
        self._validate_precision()
        self._validate_ranges()
    
    def _validate_device(self):
        """Validate and normalize device setting."""
        if self.device == "auto":
            if torch.cuda.is_available():
                self.device = "cuda"
                logger.info("Auto-selected CUDA device")
            else:
                self.device = "cpu"
                logger.info("Auto-selected CPU device")
        
        # Validate device availability
        if self.device.startswith("cuda") and not torch.cuda.is_available():
            logger.warning("CUDA not available, falling back to CPU")
            self.device = "cpu"
    
    def _validate_precision(self):
        """Validate precision setting."""
        if self.precision == "bf16" and not torch.cuda.is_bf16_supported():
            logger.warning("BF16 not supported, falling back to FP16")
            self.precision = "fp16"
        
        if self.precision in ["fp16", "bf16"] and self.device == "cpu":
            logger.warning("Mixed precision not supported on CPU, using FP32")
            self.precision = "fp32"
    
    def _validate_ranges(self):
        """Validate numeric ranges."""
        if self.max_batch_size <= 0:
            raise ValueError("max_batch_size must be positive")
        
        if self.max_sequence_length <= 0:
            raise ValueError("max_sequence_length must be positive")
        
        if not 0 < self.mlm_top_p <= 1.0:
            raise ValueError("mlm_top_p must be between 0 and 1")
        
        if self.mlm_temperature <= 0:
            raise ValueError("mlm_temperature must be positive")
        
        if self.mlm_top_k <= 0:
            raise ValueError("mlm_top_k must be positive")
    
    @property
    def torch_dtype(self) -> torch.dtype:
        """Get the corresponding PyTorch dtype."""
        if self.precision == "fp16":
            return torch.float16
        elif self.precision == "bf16":
            return torch.bfloat16
        else:
            return torch.float32
    
    @property
    def is_cuda(self) -> bool:
        """Check if using CUDA device."""
        return self.device.startswith("cuda")
    
    @property
    def is_mixed_precision(self) -> bool:
        """Check if using mixed precision."""
        return self.precision in ["fp16", "bf16"]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "device": self.device,
            "precision": self.precision,
            "torch_dtype": str(self.torch_dtype),
            "max_batch_size": self.max_batch_size,
            "max_sequence_length": self.max_sequence_length,
            "use_cache": self.use_cache,
            "mlm_top_k": self.mlm_top_k,
            "mlm_top_p": self.mlm_top_p,
            "mlm_temperature": self.mlm_temperature,
            "mlm_return_all_predictions": self.mlm_return_all_predictions,
            "embedding_pooling_strategy": self.embedding_pooling_strategy,
            "embedding_normalize": self.embedding_normalize,
            "embedding_layer": self.embedding_layer,
            "use_torch_compile": self.use_torch_compile,
            "enable_memory_efficient_attention": self.enable_memory_efficient_attention,
            "gradient_checkpointing": self.gradient_checkpointing,
            "return_attention_weights": self.return_attention_weights,
            "return_hidden_states": self.return_hidden_states,
        }
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> "InferenceConfig":
        """Create from dictionary."""
        # Filter out fields that aren't part of the dataclass
        valid_fields = {field.name for field in cls.__dataclass_fields__.values()}
        filtered_dict = {k: v for k, v in config_dict.items() if k in valid_fields}
        return cls(**filtered_dict)
