# Copyright 2024 onwards Answer.AI, LightOn, and contributors
# License: Apache-2.0

from typing import List, Dict, Any, Optional
import torch
import logging

logger = logging.getLogger(__name__)


class ValidationUtils:
    """
    Utilities for validating inference inputs and outputs.
    """
    
    @staticmethod
    def validate_texts(texts: List[str], min_length: int = 1, max_length: Optional[int] = None) -> bool:
        """
        Validate input texts for inference.
        
        Args:
            texts: List of input texts
            min_length: Minimum text length
            max_length: Maximum text length (optional)
            
        Returns:
            True if texts are valid
        """
        if not texts:
            raise ValueError("Input texts cannot be empty")
        
        if not all(isinstance(text, str) for text in texts):
            raise ValueError("All inputs must be strings")
        
        for i, text in enumerate(texts):
            if len(text) < min_length:
                raise ValueError(f"Text {i} is too short: {len(text)} < {min_length}")
            
            if max_length and len(text) > max_length:
                logger.warning(f"Text {i} will be truncated: {len(text)} > {max_length}")
        
        return True
    
    @staticmethod
    def validate_model_inputs(inputs: Dict[str, torch.Tensor], is_unpadded: bool = False) -> bool:
        """
        Validate model input tensors for padded format.

        Args:
            inputs: Dictionary of input tensors
            is_unpadded: Ignored - kept for compatibility (always uses padded validation)

        Returns:
            True if inputs are valid
        """
        # Only require padded input format
        required_keys = ["input_ids", "attention_mask"]

        for key in required_keys:
            if key not in inputs:
                raise ValueError(f"Missing required input: {key}")

        # Validate tensor properties
        input_ids = inputs["input_ids"]
        if not torch.is_tensor(input_ids):
            raise ValueError("input_ids must be a tensor")

        if input_ids.dtype not in [torch.long, torch.int]:
            raise ValueError(f"input_ids must be integer tensor, got {input_ids.dtype}")

        # Validate padded format (3D tensor: [batch_size, seq_len])
        if len(input_ids.shape) != 2:
            raise ValueError(f"input_ids must be 2D tensor for padded format, got shape {input_ids.shape}")

        return True
    
    @staticmethod
    def validate_mlm_predictions(predictions: List[Dict[str, Any]]) -> bool:
        """
        Validate MLM prediction results.
        
        Args:
            predictions: List of prediction dictionaries
            
        Returns:
            True if predictions are valid
        """
        for i, pred in enumerate(predictions):
            if not isinstance(pred, dict):
                raise ValueError(f"Prediction {i} must be a dictionary")
            
            if "predictions" not in pred:
                raise ValueError(f"Prediction {i} missing 'predictions' key")
            
            for j, token_pred in enumerate(pred["predictions"]):
                if not isinstance(token_pred, dict):
                    raise ValueError(f"Token prediction {i},{j} must be a dictionary")
                
                required_keys = ["token", "token_id", "probability"]
                for key in required_keys:
                    if key not in token_pred:
                        raise ValueError(f"Token prediction {i},{j} missing '{key}'")
        
        return True 