# Copyright 2024 onwards Answer.AI, LightOn, and contributors
# License: Apache-2.0

import torch
from typing import Optional
import logging

logger = logging.getLogger(__name__)


class AttentionUtils:
    """
    Utilities for handling attention operations during inference.
    """
    
    @staticmethod
    def create_causal_mask(seq_len: int, device: torch.device) -> torch.Tensor:
        """
        Create a causal attention mask.
        
        Args:
            seq_len: Sequence length
            device: Target device
            
        Returns:
            Causal mask tensor
        """
        mask = torch.triu(torch.ones(seq_len, seq_len, device=device), diagonal=1)
        return mask.bool()
    
    @staticmethod
    def create_padding_mask(input_ids: torch.Tensor, pad_token_id: int) -> torch.Tensor:
        """
        Create padding mask from input IDs.
        
        Args:
            input_ids: Input token IDs
            pad_token_id: Padding token ID
            
        Returns:
            Padding mask (1 for valid tokens, 0 for padding)
        """
        return (input_ids != pad_token_id).long()
    
    @staticmethod 
    def get_attention_scores(
        attention_weights: torch.Tensor,
        input_ids: torch.Tensor,
        tokenizer,
        layer_idx: Optional[int] = None,
        head_idx: Optional[int] = None
    ) -> dict:
        """
        Extract and format attention scores for analysis.
        
        Args:
            attention_weights: Attention weight tensors
            input_ids: Input token IDs
            tokenizer: Tokenizer for decoding
            layer_idx: Specific layer to extract (if None, returns all)
            head_idx: Specific head to extract (if None, returns all)
            
        Returns:
            Dictionary with formatted attention scores
        """
        if not isinstance(attention_weights, (tuple, list)):
            attention_weights = [attention_weights]
        
        tokens = tokenizer.convert_ids_to_tokens(input_ids[0])
        
        result = {
            "tokens": tokens,
            "layers": {}
        }
        
        for i, layer_attn in enumerate(attention_weights):
            if layer_idx is not None and i != layer_idx:
                continue
                
            layer_result = {"heads": {}}
            
            for j in range(layer_attn.shape[1]):  # num_heads
                if head_idx is not None and j != head_idx:
                    continue
                    
                head_attn = layer_attn[0, j].cpu().numpy()  # [seq_len, seq_len]
                layer_result["heads"][f"head_{j}"] = head_attn.tolist()
            
            result["layers"][f"layer_{i}"] = layer_result
        
        return result 