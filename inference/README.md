# ModernBERT Inference Pipeline

A comprehensive inference system for ModernBERT models supporting both MLM (Masked Language Modeling) and sentence embedding generation with automatic adaptation to padding/unpadding configurations.

## Features

- **🎯 Dual Task Support**: MLM mask-fill and sentence embedding generation
- **🔧 Auto-Configuration**: Automatically adapts to padding/unpadding based on training config
- **⚡ Efficient Batching**: Memory-efficient batch processing for large datasets
- **🎨 Multiple Pooling**: Support for various pooling strategies (mean, cls, max, mean_sqrt_len)
- **🔄 HuggingFace Compatible**: Familiar API compatible with HuggingFace pipelines
- **📊 Performance Monitoring**: Built-in timing and memory usage tracking

## Quick Start

### Basic Usage

```python
from inference import ModernBERTInference

# Initialize inference from training config and checkpoint
inference = ModernBERTInference(
    config_path="path/to/training_config.yaml",
    checkpoint_path="path/to/checkpoint.pt",
    device="auto",
    precision="fp32"
)

# MLM inference
predictions = inference.predict_masked_tokens(
    "The capital of France is [MASK].",
    top_k=5
)

# Embedding generation
embeddings = inference.encode_texts([
    "This is a sentence.",
    "This is another sentence."
])

# Cleanup resources
inference.cleanup()
```

### Context Manager Usage (Recommended)

```python
with ModernBERTInference(config_path, checkpoint_path) as inference:
    # Use inference here
    predictions = inference.predict_masked_tokens(text)
    embeddings = inference.encode_texts(texts)
    # Automatic cleanup on exit
```

## Installation & Setup

### Prerequisites

1. **Environment**: Activate the `bert24` conda environment for optimal performance:
   ```bash
   conda activate bert24
   ```

2. **Dependencies**: Ensure you have all required dependencies installed (see main project requirements)

3. **Model Files**: You need:
   - A trained ModernBERT checkpoint (`.pt` file)
   - The corresponding training configuration YAML file

### Directory Structure

```
inference/
├── __init__.py                 # Main API exports
├── README.md                   # This documentation
├── inference.py                # Main inference interface
├── config/                     # Configuration management
│   ├── model_config.py        # Training config parser
│   └── inference_config.py    # Inference settings
├── core/                       # Core infrastructure
│   ├── checkpoint_manager.py  # Checkpoint loading
│   ├── model_factory.py       # Model instantiation
│   └── input_processor.py     # Text processing
├── pipelines/                  # Task-specific pipelines
│   ├── base_pipeline.py       # Base pipeline class
│   ├── mlm_pipeline.py        # MLM inference
│   └── embedding_pipeline.py  # Embedding generation
├── utils/                      # Utility functions
│   ├── padding_utils.py       # Padding/unpadding ops
│   ├── attention_utils.py     # Attention utilities
│   └── validation_utils.py    # Input validation
└── examples/                   # Example scripts
    ├── mlm_example.py          # MLM usage examples
    ├── embedding_example.py    # Embedding examples
    └── batch_inference_example.py # Batch processing
```

## API Reference

### ModernBERTInference

Main inference interface providing both MLM and embedding capabilities.

#### Initialization

```python
ModernBERTInference(
    config_path: str,           # Path to training YAML config
    checkpoint_path: str,       # Path to model checkpoint
    device: str = "auto",       # Device ("auto", "cpu", "cuda")
    precision: str = "fp32",    # Precision ("fp32", "fp16", "bf16")
    **inference_kwargs          # Additional config parameters
)
```

#### MLM Methods

**predict_masked_tokens()**
```python
inference.predict_masked_tokens(
    texts: Union[str, List[str]],  # Input text(s) with [MASK] tokens
    top_k: int = 10,               # Number of top predictions
    top_p: float = 0.9,            # Nucleus sampling threshold
    temperature: float = 1.0        # Sampling temperature
) -> Union[Dict, List[Dict]]
```

**fill_mask()** (HuggingFace compatible)
```python
inference.fill_mask(
    text: str,                   # Input text with mask
    mask_token: str = "[MASK]",  # Mask token to replace
    return_top_k: int = 5        # Number of predictions
) -> List[Dict[str, Any]]
```

**evaluate_perplexity()**
```python
inference.evaluate_perplexity(
    texts: List[str],              # Texts to evaluate
    mask_probability: float = 0.15  # Masking probability
) -> List[Dict[str, Any]]
```

#### Embedding Methods

**encode_texts()**
```python
inference.encode_texts(
    texts: Union[str, List[str]],    # Input text(s)
    pooling_strategy: str = "mean",  # Pooling strategy
    normalize: bool = True,          # L2 normalization
    batch_size: Optional[int] = None # Batch size
) -> np.ndarray
```

**compute_similarity()**
```python
inference.compute_similarity(
    texts1: Union[str, List[str]],   # First set of texts
    texts2: Union[str, List[str]],   # Second set of texts
    similarity_fn: str = "cosine"    # Similarity function
) -> Union[float, np.ndarray]
```

### Configuration Options

#### Inference Config Parameters

```python
InferenceConfig(
    # Device and precision
    device: str = "auto",
    precision: str = "fp32",
    
    # Batch processing
    max_batch_size: int = 32,
    max_sequence_length: int = 512,
    
    # MLM settings
    mlm_top_k: int = 10,
    mlm_top_p: float = 0.9,
    mlm_temperature: float = 1.0,
    
    # Embedding settings
    embedding_pooling_strategy: str = "mean",
    embedding_normalize: bool = True,
    embedding_layer: int = -1,
    
    # Advanced options
    return_hidden_states: bool = False,
    use_cache: bool = True,
    low_memory_mode: bool = False
)
```

#### Supported Pooling Strategies

- **`mean`**: Mean pooling over sequence (default for embeddings)
- **`cls`**: Use CLS token representation
- **`max`**: Max pooling over sequence
- **`mean_sqrt_len`**: Mean pooling normalized by sqrt(length)

#### Automatic Padding/Unpadding Detection

The system automatically detects the padding configuration from your training YAML:

```yaml
model:
  model_config:
    padding: unpadded        # Detected automatically
    unpad_embeddings: true   # Handled automatically
```

## Examples

### MLM Examples

#### Single Mask Prediction
```python
text = "The capital of France is [MASK]."
predictions = inference.predict_masked_tokens(text, top_k=5)

for pred in predictions["mask_predictions"][0]["predictions"]:
    print(f"{pred['token']}: {pred['probability']:.3f}")
```

#### Multiple Masks
```python
text = "The [MASK] of [MASK] is Paris."
predictions = inference.predict_masked_tokens(text)

for i, mask_pred in enumerate(predictions["mask_predictions"]):
    print(f"Mask {i+1}:")
    for pred in mask_pred["predictions"][:3]:
        print(f"  {pred['token']}: {pred['probability']:.3f}")
```

#### Batch Processing
```python
texts = [
    "The weather is [MASK].",
    "I love [MASK] programming.",
    "AI will [MASK] the future."
]

batch_predictions = inference.batch_predict_masked_tokens(texts, batch_size=8)
```

### Embedding Examples

#### Single Text Encoding
```python
text = "Natural language processing is fascinating."
embedding = inference.encode_texts(text)
print(f"Embedding shape: {embedding.shape}")
```

#### Batch Encoding
```python
sentences = [
    "I love machine learning.",
    "Deep learning is powerful.",
    "NLP is exciting."
]

embeddings = inference.encode_texts(sentences, normalize=True)
print(f"Embeddings shape: {embeddings.shape}")
```

#### Similarity Computation
```python
similarity = inference.compute_similarity(
    "I enjoy AI research.",
    "Machine learning is fun.",
    similarity_fn="cosine"
)
print(f"Similarity: {similarity:.3f}")
```

#### Different Pooling Strategies
```python
text = "Compare pooling strategies."

for strategy in ["mean", "cls", "max"]:
    embedding = inference.encode_texts(text, pooling_strategy=strategy)
    print(f"{strategy}: norm = {np.linalg.norm(embedding):.3f}")
```

## Performance Optimization

### Batch Size Tuning

```python
# Test different batch sizes to find optimal performance
batch_sizes = [1, 4, 8, 16, 32]
texts = ["sample text"] * 100

for batch_size in batch_sizes:
    start_time = time.time()
    embeddings = inference.batch_encode_texts(texts, batch_size=batch_size)
    elapsed = time.time() - start_time
    print(f"Batch size {batch_size}: {elapsed:.2f}s ({len(texts)/elapsed:.1f} samples/s)")
```

### Memory Management

```python
# For large datasets, use smaller batch sizes
large_texts = ["text"] * 10000

# Process in memory-efficient batches
embeddings = inference.batch_encode_texts(
    large_texts, 
    batch_size=8  # Adjust based on available GPU memory
)
```

### GPU Memory Optimization

```python
# Use mixed precision for faster inference
inference = ModernBERTInference(
    config_path=config_path,
    checkpoint_path=checkpoint_path,
    precision="fp16",  # or "bf16"
    max_batch_size=64  # Increase batch size with lower precision
)
```

## Troubleshooting

### Common Issues

#### 1. Out of Memory Errors
```python
# Reduce batch size
inference_config = InferenceConfig(max_batch_size=4)

# Use lower precision
inference = ModernBERTInference(..., precision="fp16")

# Enable low memory mode
inference_config = InferenceConfig(low_memory_mode=True)
```

#### 2. Slow Performance
```python
# Ensure you're using GPU
inference = ModernBERTInference(..., device="cuda")

# Use larger batch sizes if memory allows
inference_config = InferenceConfig(max_batch_size=32)

# Activate bert24 environment
# conda activate bert24
```

#### 3. Configuration Mismatch
```python
# Validate configuration before use
if inference.validate_configuration():
    print("Configuration is valid")
else:
    print("Configuration mismatch detected")
```

### Error Messages

- **"Model configuration incompatible with checkpoint"**: The YAML config doesn't match the checkpoint architecture
- **"Tokenizer not found"**: Update the `tokenizer_name` path in your config
- **"CUDA out of memory"**: Reduce `max_batch_size` or use `precision="fp16"`

## Advanced Usage

### Custom Configuration

```python
from inference.config import InferenceConfig

# Create custom inference config
config = InferenceConfig(
    device="cuda:1",
    precision="bf16",
    max_batch_size=64,
    embedding_pooling_strategy="cls",
    mlm_temperature=0.8
)

inference = ModernBERTInference(
    config_path=config_path,
    checkpoint_path=checkpoint_path,
    **config.to_dict()
)
```

### Standalone Pipelines

```python
from inference import create_mlm_inference, create_embedding_inference

# Create standalone MLM pipeline
mlm_pipeline = create_mlm_inference(config_path, checkpoint_path)
predictions = mlm_pipeline.predict(["Text with [MASK]."])

# Create standalone embedding pipeline
embedding_pipeline = create_embedding_inference(config_path, checkpoint_path)
embeddings = embedding_pipeline.predict(["Text to encode."])
```

### Integration with Other Libraries

#### With sentence-transformers
```python
# Our interface is similar to sentence-transformers
sentences = ["sentence 1", "sentence 2"]
embeddings = inference.encode_texts(sentences)  # Similar to model.encode()
```

#### With scikit-learn
```python
from sklearn.cluster import KMeans

# Generate embeddings for clustering
texts = ["doc1", "doc2", "doc3"]
embeddings = inference.encode_texts(texts, normalize=True)

# Use with scikit-learn
kmeans = KMeans(n_clusters=2)
clusters = kmeans.fit_predict(embeddings)
```

## Contributing

To extend the inference system:

1. **Add new tasks**: Create new pipeline classes inheriting from `BasePipeline`
2. **Add new pooling strategies**: Extend the pooling methods in embedding pipeline
3. **Optimize performance**: Contribute optimizations to core components

## License

Copyright 2024 onwards Answer.AI, LightOn, and contributors. Licensed under Apache-2.0. 