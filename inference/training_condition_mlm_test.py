#!/usr/bin/env python3
"""
MLM Accuracy Test Under Training Conditions

This script tests MLM accuracy using the exact same conditions as training:
- 1024 token sequences
- 30% masking rate (training) / 15% masking rate (evaluation)
- Full document context
- Proper 80/10/10 masking strategy
"""

import sys
import os
from pathlib import Path
import torch
import numpy as np
from transformers import AutoTokenizer, DataCollatorForLanguageModeling
from collections import defaultdict
import time

# Add project root to path
sys.path.insert(0, str(Path.cwd()))

from inference import ModernBERTInference

def comprehensive_mlm_accuracy_test():
    """Test MLM accuracy under exact training conditions."""
    
    print("🎯 Comprehensive MLM Accuracy Test Under Training Conditions")
    print("=" * 80)
    
    # Configuration matching training
    # config_path = "yamls/main/flex-bert-modernbert-base-edu-fw-320B-paper-custom-tokenizer.yaml"
    config_path = "yamls/main/flex-bert-modernbert-large-edu-fw-320B-paper-custom-tokenizer.yaml"
    # checkpoint_path = "/s2_nfs/ckpt/flex-bert-modernbert-base-edu-fw-320B-article-custom-tokenizer/ep3-ba339282-rank0.pt"
    checkpoint_path = "/s2_nfs/ckpt/flex-bert-modernbert-large-edu-fw-320B-article-custom-tokenizer/ep1-ba105922-rank0.pt"
    tokenizer_path = "/s2_nfs/tokenizer_spm_50368_140B_EnFa_hf_edited"
    
    # Training parameters
    max_seq_len = 1024
    training_mlm_prob = 0.3
    eval_mlm_prob = 0.15
    
    print(f"Configuration:")
    print(f"  - Model config: {config_path}")
    print(f"  - Checkpoint: {checkpoint_path}")
    print(f"  - Max sequence length: {max_seq_len}")
    print(f"  - Training MLM probability: {training_mlm_prob}")
    print(f"  - Evaluation MLM probability: {eval_mlm_prob}")
    
    # Load tokenizer and model
    print(f"\n🚀 Loading model and tokenizer...")
    tokenizer = AutoTokenizer.from_pretrained(tokenizer_path)
    
    with ModernBERTInference(config_path, checkpoint_path) as inference:
        print(f"  ✓ Model loaded successfully")
        
        # Test with different document types and lengths
        test_documents = get_test_documents()
        
        # Test under both training and evaluation conditions
        for condition_name, mlm_prob in [("Training", training_mlm_prob), ("Evaluation", eval_mlm_prob)]:
            print(f"\n{'='*60}")
            print(f"🧪 Testing under {condition_name} conditions (MLM prob: {mlm_prob})")
            print(f"{'='*60}")
            
            test_mlm_accuracy_condition(
                inference, tokenizer, test_documents, 
                condition_name, mlm_prob, max_seq_len
            )

def get_test_documents():
    """Get diverse test documents of different lengths and types."""
    
    return [
        {
            "name": "Short factual",
            "text": "The capital of France is Paris. Paris is located in the north-central part of France."
        },
        {
            "name": "Medium educational",
            "text": """Machine learning is a subset of artificial intelligence that enables computers to learn 
            and improve from experience without being explicitly programmed. It focuses on the development 
            of computer programs that can access data and use it to learn for themselves. The process of 
            learning begins with observations or data, such as examples, direct experience, or instruction, 
            in order to look for patterns in data and make better decisions in the future."""
        },
        {
            "name": "Long scientific",
            "text": """Climate change refers to long-term shifts in global temperatures and weather patterns. 
            While climate variations are natural, since the mid-20th century, human activities have been 
            the main driver of climate change, primarily due to the burning of fossil fuels like coal, 
            oil and gas. Burning fossil fuels generates greenhouse gas emissions that act like a blanket 
            wrapped around the Earth, trapping the sun's heat and raising temperatures. The main greenhouse 
            gases that are causing climate change include carbon dioxide and methane. These come from using 
            gasoline for driving a car or coal for heating a building, for example. Clearing land and cutting 
            down forests can also release carbon dioxide. Agriculture, oil and gas operations are major 
            sources of methane emissions. Energy, industry, transport, buildings, agriculture and land use 
            are among the main sectors causing greenhouse gas emissions."""
        },
        {
            "name": "Very long narrative",
            "text": """The history of artificial intelligence began in antiquity, with myths, stories and rumors 
            of artificial beings endowed with intelligence or consciousness by master craftsmen. The seeds 
            of modern AI were planted by classical philosophers who attempted to describe the process of 
            human thinking as the mechanical manipulation of symbols. This work culminated in the invention 
            of the programmable digital computer in the 1940s, a machine based on the abstract essence of 
            mathematical reasoning. This device and the ideas behind it inspired a handful of scientists 
            to begin seriously discussing the possibility of building an electronic brain. The field of 
            artificial intelligence research was founded at a workshop held on the campus of Dartmouth 
            College during the summer of 1956. Those who attended would become the leaders of AI research 
            for decades. Many of them predicted that a machine as intelligent as a human being would exist 
            in no more than a generation, and they were given millions of dollars to make this vision come 
            true. Eventually, it became obvious that commercial developers and researchers had been 
            overly optimistic. Hans Moravec, Claude Shannon, Marvin Minsky and others predicted that AI 
            would be achieved in the 1960s. When this proved untrue, funding was cut and the field went 
            through several AI winters. The field was revived in the 1980s when the British government 
            started funding AI research again in response to the competitive threat from Japan's fifth 
            generation computer project."""
        }
    ]

def test_mlm_accuracy_condition(inference, tokenizer, test_documents, condition_name, mlm_prob, max_seq_len):
    """Test MLM accuracy under specific conditions."""
    
    # Create data collator for this condition
    collator = DataCollatorForLanguageModeling(
        tokenizer=tokenizer,
        mlm=True,
        mlm_probability=mlm_prob
    )
    
    total_predictions = 0
    correct_predictions = 0
    top5_correct_predictions = 0
    
    results_by_doc_type = defaultdict(lambda: {"total": 0, "correct": 0, "top5_correct": 0})
    
    for doc in test_documents:
        print(f"\n--- {doc['name']} ---")
        print(f"Text length: {len(doc['text'])} chars")
        
        # Test multiple times for statistical significance
        num_trials = 5
        doc_total = 0
        doc_correct = 0
        doc_top5_correct = 0
        
        for trial in range(num_trials):
            trial_total, trial_correct, trial_top5_correct = test_single_document(
                inference, tokenizer, collator, doc['text'], max_seq_len, trial
            )
            
            doc_total += trial_total
            doc_correct += trial_correct
            doc_top5_correct += trial_top5_correct
        
        # Document results
        if doc_total > 0:
            doc_accuracy = doc_correct / doc_total * 100
            doc_top5_accuracy = doc_top5_correct / doc_total * 100
            print(f"  Results: {doc_correct}/{doc_total} correct ({doc_accuracy:.1f}%)")
            print(f"  Top-5: {doc_top5_correct}/{doc_total} correct ({doc_top5_accuracy:.1f}%)")
        else:
            print(f"  No tokens were masked in this document")
        
        # Accumulate totals
        total_predictions += doc_total
        correct_predictions += doc_correct
        top5_correct_predictions += doc_top5_correct
        
        results_by_doc_type[doc['name']]["total"] = doc_total
        results_by_doc_type[doc['name']]["correct"] = doc_correct
        results_by_doc_type[doc['name']]["top5_correct"] = doc_top5_correct
    
    # Overall results for this condition
    print(f"\n🎯 {condition_name} Condition Overall Results:")
    if total_predictions > 0:
        overall_accuracy = correct_predictions / total_predictions * 100
        overall_top5_accuracy = top5_correct_predictions / total_predictions * 100
        print(f"  Total masked tokens tested: {total_predictions}")
        print(f"  Top-1 accuracy: {correct_predictions}/{total_predictions} ({overall_accuracy:.1f}%)")
        print(f"  Top-5 accuracy: {top5_correct_predictions}/{total_predictions} ({overall_top5_accuracy:.1f}%)")
        
        # Compare with reported training accuracy
        if condition_name == "Evaluation":
            print(f"  📊 Comparison with reported ~80% training accuracy:")
            if overall_accuracy >= 70:
                print(f"     ✅ Good: {overall_accuracy:.1f}% is close to expected performance")
            elif overall_accuracy >= 50:
                print(f"     ⚠️  Moderate: {overall_accuracy:.1f}% is lower than expected but reasonable")
            else:
                print(f"     ❌ Poor: {overall_accuracy:.1f}% is significantly lower than expected")
    else:
        print(f"  ❌ No tokens were masked across all documents")

def test_single_document(inference, tokenizer, collator, text, max_seq_len, trial_num):
    """Test MLM accuracy on a single document."""
    
    # Tokenize with proper length
    encoded = tokenizer(
        text,
        truncation=True,
        max_length=max_seq_len,
        padding='max_length',
        return_tensors='pt'
    )
    
    # Apply MLM masking
    batch = collator([{
        'input_ids': encoded['input_ids'].squeeze(),
        'attention_mask': encoded['attention_mask'].squeeze()
    }])
    
    input_ids = batch['input_ids'].squeeze()
    labels = batch['labels'].squeeze()
    attention_mask = batch['attention_mask'].squeeze()
    
    # Find masked positions (where labels != -100)
    masked_positions = (labels != -100).nonzero(as_tuple=False).flatten()
    
    if len(masked_positions) == 0:
        return 0, 0, 0  # No tokens masked
    
    print(f"    Trial {trial_num+1}: {len(masked_positions)} tokens masked")
    
    # Get model predictions for masked positions
    total_correct = 0
    top5_correct = 0
    
    # Convert to the format expected by inference pipeline
    # We need to create a text with explicit <mask> tokens
    masked_text = create_explicit_mask_text(tokenizer, input_ids, masked_positions)
    
    try:
        # Get predictions from inference pipeline
        predictions = inference.predict_masked_tokens(masked_text, top_k=5)
        
        if predictions and 'mask_predictions' in predictions:
            for i, mask_pred in enumerate(predictions['mask_predictions']):
                if i < len(masked_positions):
                    original_pos = masked_positions[i]
                    true_token_id = labels[original_pos].item()
                    true_token = tokenizer.decode([true_token_id])
                    
                    if mask_pred['predictions']:
                        # Check top-1 accuracy
                        pred_token = mask_pred['predictions'][0]['token']
                        if pred_token.strip() == true_token.strip():
                            total_correct += 1
                        
                        # Check top-5 accuracy
                        top5_tokens = [p['token'].strip() for p in mask_pred['predictions'][:5]]
                        if true_token.strip() in top5_tokens:
                            top5_correct += 1
    
    except Exception as e:
        print(f"      Error during prediction: {e}")
        return len(masked_positions), 0, 0
    
    return len(masked_positions), total_correct, top5_correct

def create_explicit_mask_text(tokenizer, input_ids, masked_positions):
    """Create text with explicit <mask> tokens for inference."""
    
    # Convert input_ids back to text, replacing masked positions with <mask>
    tokens = []
    for i, token_id in enumerate(input_ids):
        if i in masked_positions:
            tokens.append(tokenizer.mask_token)
        elif token_id != tokenizer.pad_token_id:
            tokens.append(tokenizer.decode([token_id]))
    
    return ''.join(tokens)

if __name__ == "__main__":
    comprehensive_mlm_accuracy_test()
