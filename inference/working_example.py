#!/usr/bin/env python3
"""
Working example of how to use the ModernBERT inference system.

This demonstrates the exact code to use in your Jupyter notebook.
"""

import sys
from pathlib import Path

# Add project root to path (use this in Jupyter notebook)
sys.path.insert(0, str(Path.cwd()))

# Import directly from the module (bypasses problematic package imports)
from inference import ModernBERTInference

def main():
    """Demo of the inference system."""
    
    # Your actual paths
    config_path = "yamls/main/flex-bert-modernbert-base-edu-fw-320B-paper-custom-tokenizer.yaml"
    # checkpoint_path = "/s2_nfs/ckpt/flex-bert-modernbert-base-edu-fw-320B-article-custom-tokenizer/ep3-ba339282-rank0.pt"
    checkpoint_path = "/s2_nfs/ckpt/flex-bert-modernbert-base-edu-fw-320B-article-custom-tokenizer/ep1-ba114000-rank0.pt"
    
    
    print("🚀 ModernBERT Inference Example")
    print("=" * 50)
    
    try:
        # Initialize inference system
        print("1. Initializing inference system...")
        with ModernBERTInference(
            config_path=config_path,
            checkpoint_path=checkpoint_path,
            device="auto",
            precision="fp32"
        ) as inference:
            
            # Get model info
            print("2. Getting model information...")
            model_info = inference.get_model_info()
            print(f"   ✓ Model loaded: {model_info.get('parameter_count', 'Unknown')} parameters")
            print(f"   ✓ Device: {model_info.get('device', 'Unknown')}")
            print(f"   ✓ Padding: {'unpadded' if model_info.get('is_unpadded', False) else 'padded'}")
            
            # MLM inference example
            print("\n3. Testing MLM inference...")
            text_with_mask = "The capital of France is <mask>."
            print(f"   Input: {text_with_mask}")
            
            predictions = inference.predict_masked_tokens(text_with_mask, top_k=3)
            print("   Predictions:")
            
            if isinstance(predictions, dict) and "mask_predictions" in predictions:
                for mask_pred in predictions["mask_predictions"]:
                    for i, pred in enumerate(mask_pred["predictions"][:3]):
                        print(f"     {i+1}. {pred['token']} (prob: {pred['probability']:.3f})")
            
            # Embedding generation example
            print("\n4. Testing embedding generation...")
            sentences = [
                "This is a sentence about machine learning.",
                "The apple is red."
            ]
            
            embeddings = inference.encode_texts(sentences, normalize=True)
            print(f"   Generated embeddings shape: {embeddings.shape}")
            print(f"   Embedding dimension: {inference.get_embedding_dimension()}")
            
            # Similarity computation
            similarity = inference.compute_similarity(sentences[0], sentences[1])
            print(f"   Similarity between sentences: {similarity:.3f}")
            
            print("\n✅ All tests completed successfully!")
            print("🎉 Your inference system is working correctly!")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        
        print("\n🔧 Troubleshooting tips:")
        print("1. Make sure your checkpoint path is correct")
        print("2. Verify the config YAML file exists")
        print("3. Check that you have enough GPU memory")
        print("4. Try setting device='cpu' if GPU issues persist")

if __name__ == "__main__":
    main()
    
    print("\n" + "=" * 60)
    print("📝 FOR JUPYTER NOTEBOOK USE:")
    print("=" * 60)
    print("""
# Copy this exact code into your Jupyter notebook:

import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path.cwd()))

# Import the inference class
from inference.inference import ModernBERTInference

# Use your actual paths
config_path = "yamls/main/flex-bert-modernbert-base-edu-fw-320B-paper-custom-tokenizer.yaml"
checkpoint_path = "/s2_nfs/ckpt/flex-bert-modernbert-base-edu-fw-320B-article-custom-tokenizer/ep3-ba339282-rank0.pt"

# Initialize and use
with ModernBERTInference(config_path, checkpoint_path) as inference:
    # MLM inference
    predictions = inference.predict_masked_tokens("The capital of France is <mask>.")
    print("Predictions:", predictions)
    
    # Embedding generation  
    embeddings = inference.encode_texts(["This is a sentence.", "This is another sentence."])
    print("Embeddings shape:", embeddings.shape)
""") 