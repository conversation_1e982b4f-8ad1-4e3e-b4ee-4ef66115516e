#!/usr/bin/env python3
"""
Debug script to compare MLM inference between our simple script and the inference directory.
This will help identify why the inference directory only predicts punctuation.
"""

import os
import sys
import torch
from pathlib import Path

# Add src directory to path
sys.path.append(os.path.dirname(os.path.realpath(__file__)))

# Import our simple implementation
from src.bert_layers.configuration_bert import FlexBertConfig
from src.bert_layers.model import FlexBertForMaskedLM
from transformers import AutoTokenizer
from omegaconf import OmegaConf

# Import inference directory implementation
sys.path.insert(0, str(Path.cwd()))
from inference.inference import ModernBERTInference

# Configuration
CONFIG_PATH = "yamls/main/flex-bert-modernbert-base-edu-fw-320B-paper-custom-tokenizer.yaml"
CHECKPOINT_PATH = "/s2_nfs/ckpt/flex-bert-modernbert-base-edu-fw-320B-article-custom-tokenizer/ep3-ba339282-rank0.pt"
INPUT_TEXT = "The capital of France is <mask>."
DEVICE = "cuda" if torch.cuda.is_available() else "cpu"

def load_config(config_path: str):
    """Load configuration like our simple script does."""
    config_path = Path(config_path)
    with open(config_path) as f:
        config = OmegaConf.load(f)
    
    defaults_path = config_path.parent.parent / "defaults.yaml"
    if defaults_path.exists():
        with open(defaults_path) as f:
            default_config = OmegaConf.load(f)
        config = OmegaConf.merge(default_config, config)
    
    OmegaConf.resolve(config)
    return OmegaConf.to_container(config, resolve=True)

def debug_simple_approach():
    """Debug our simple MLM approach."""
    print("=" * 60)
    print("DEBUGGING SIMPLE APPROACH (mlm_inference.py)")
    print("=" * 60)
    
    # Load config and tokenizer
    config = load_config(CONFIG_PATH)
    tokenizer_name = config.get("tokenizer_name", "bert-base-uncased")
    
    try:
        tokenizer = AutoTokenizer.from_pretrained(
            tokenizer_name,
            use_fast=True,
            trust_remote_code=True
        )
    except:
        tokenizer = AutoTokenizer.from_pretrained("bert-base-uncased")
    
    print(f"Tokenizer: {tokenizer_name}")
    print(f"Vocab size: {len(tokenizer)}")
    print(f"Mask token: {tokenizer.mask_token}")
    print(f"Mask token ID: {tokenizer.mask_token_id}")
    
    # Create model config
    model_config = config["model"]["model_config"]
    config_dict = dict(model_config)
    config_dict['vocab_size'] = len(tokenizer)
    bert_config = FlexBertConfig(**config_dict)
    
    print(f"Model config - padding: {bert_config.padding}")
    print(f"Model config - unpad_embeddings: {bert_config.unpad_embeddings}")
    print(f"Model config - pad_logits: {bert_config.pad_logits}")
    
    # Create and load model
    model = FlexBertForMaskedLM(bert_config)
    checkpoint = torch.load(CHECKPOINT_PATH, map_location="cpu")
    
    if "state" in checkpoint and "model" in checkpoint["state"]:
        state_dict = checkpoint["state"]["model"]
    else:
        state_dict = checkpoint
    
    # Remove model. prefix if present
    if any(key.startswith("model.") for key in state_dict.keys()):
        new_state_dict = {}
        for key, value in state_dict.items():
            if key.startswith("model."):
                new_key = key[6:]
                new_state_dict[new_key] = value
            else:
                new_state_dict[key] = value
        state_dict = new_state_dict
    
    model.load_state_dict(state_dict)
    model = model.to(DEVICE)
    model.eval()
    
    # Tokenize input
    inputs = tokenizer(INPUT_TEXT, return_tensors="pt", padding=True, truncation=True)
    inputs = {k: v.to(DEVICE) for k, v in inputs.items()}
    
    print(f"Input text: {INPUT_TEXT}")
    print(f"Input IDs: {inputs['input_ids']}")
    print(f"Input IDs shape: {inputs['input_ids'].shape}")
    
    # Find mask positions
    mask_token_id = tokenizer.mask_token_id
    mask_positions = (inputs["input_ids"] == mask_token_id).nonzero(as_tuple=True)
    print(f"Mask positions: {list(zip(mask_positions[0].tolist(), mask_positions[1].tolist()))}")
    
    # Run model
    with torch.no_grad():
        outputs = model(**inputs)
        logits = outputs.logits
    
    print(f"Output logits shape: {logits.shape}")
    print(f"Logits type: {type(outputs)}")
    
    # Process first mask
    if len(mask_positions[0]) > 0:
        batch_idx, seq_pos = mask_positions[0][0], mask_positions[1][0]
        mask_logits = logits[batch_idx, seq_pos]
        
        top_k_logits, top_k_indices = torch.topk(mask_logits, 5)
        top_k_probs = torch.softmax(top_k_logits, dim=-1)
        
        print(f"Top 5 predictions (Simple approach):")
        for i in range(5):
            token_id = top_k_indices[i].item()
            token = tokenizer.decode([token_id])
            prob = top_k_probs[i].item()
            print(f"  {i+1}. '{token}' (ID: {token_id}, prob: {prob:.3f})")

def debug_inference_approach():
    """Debug the inference directory approach."""
    print("\n" + "=" * 60)
    print("DEBUGGING INFERENCE DIRECTORY APPROACH")
    print("=" * 60)

    with ModernBERTInference(
        config_path=CONFIG_PATH,
        checkpoint_path=CHECKPOINT_PATH,
        device="auto",
        precision="fp32"
    ) as inference:

        # Get model info
        model_info = inference.get_model_info()
        print(f"Model info: {model_info}")

        # Access the internal pipeline to debug step by step
        mlm_pipeline = inference._get_mlm_pipeline()

        # Prepare inputs manually to debug
        print("\n--- Debugging Input Processing ---")
        model_inputs = mlm_pipeline.input_processor.prepare_mlm_inputs([INPUT_TEXT])
        print(f"Model inputs keys: {model_inputs.keys()}")
        print(f"Input IDs shape: {model_inputs['input_ids'].shape}")
        print(f"Input IDs: {model_inputs['input_ids']}")

        # Run model manually to debug
        print("\n--- Debugging Model Inference ---")
        model_outputs = mlm_pipeline._run_model(model_inputs)
        logits = model_outputs.logits
        print(f"Model output logits shape: {logits.shape}")

        # Find mask positions manually
        print("\n--- Debugging Mask Position Finding ---")
        mask_token_id = mlm_pipeline.input_processor.tokenizer.mask_token_id
        print(f"Mask token ID: {mask_token_id}")

        if len(logits.shape) == 2:  # Unpadded
            mask_indices = (model_inputs['input_ids'] == mask_token_id).nonzero(as_tuple=False).flatten()
            print(f"Mask indices (unpadded): {mask_indices}")
            if len(mask_indices) > 0:
                mask_idx = mask_indices[0].item()
                mask_logits = logits[mask_idx]
                print(f"Mask logits shape: {mask_logits.shape}")

                # Get top 5 predictions manually
                top_k_logits, top_k_indices = torch.topk(mask_logits, 5)
                top_k_probs = torch.softmax(top_k_logits, dim=-1)

                print("Top 5 predictions (manual debug):")
                for i in range(5):
                    token_id = top_k_indices[i].item()
                    token = mlm_pipeline.input_processor.tokenizer.decode([token_id])
                    prob = top_k_probs[i].item()
                    print(f"  {i+1}. '{token}' (ID: {token_id}, prob: {prob:.3f})")

                # Compare with the simple approach logits
                print(f"\n--- Comparing Logits Values ---")
                print(f"Inference logits for token 260 (period): {mask_logits[260]:.6f}")
                print(f"Inference logits for token 47304 (Orléans): {mask_logits[47304]:.6f}")
                print(f"Inference logits for token 21141 (top pred): {mask_logits[21141]:.6f}")

                # Check if we're getting the right mask position
                print(f"Mask position in unpadded: {mask_idx}")
                print(f"Total unpadded length: {model_inputs['input_ids'].shape[0]}")
                print(f"Input IDs around mask: {model_inputs['input_ids'][max(0, mask_idx-2):mask_idx+3]}")

        # Now run the actual prediction to see where it differs
        print("\n--- Running Full Pipeline ---")
        predictions = inference.predict_masked_tokens(INPUT_TEXT, top_k=5)
        print(f"Inference predictions: {predictions}")

        # Extract top predictions for comparison
        if isinstance(predictions, dict) and "mask_predictions" in predictions:
            print("Top 5 predictions (Inference approach):")
            for mask_pred in predictions["mask_predictions"]:
                for i, pred in enumerate(mask_pred["predictions"][:5]):
                    print(f"  {i+1}. '{pred['token']}' (ID: {pred.get('token_id', 'N/A')}, prob: {pred['probability']:.3f})")

def main():
    """Main comparison function."""
    print("🔍 MLM INFERENCE COMPARISON DEBUG")
    print("=" * 80)
    
    try:
        debug_simple_approach()
        debug_inference_approach()
        
        print("\n" + "=" * 80)
        print("🎯 ANALYSIS COMPLETE")
        print("=" * 80)
        
    except Exception as e:
        print(f"Error during debugging: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
