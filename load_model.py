import os
import torch
from omegaconf import DictConfig, OmegaConf
from src.flex_bert import create_flex_bert_mlm
from src.bert_layers.configuration_bert import FlexBertConfig
from src.bert_layers import FlexBertModel
import src.bert_layers.configuration_bert as configuration_bert_module

# We need these two for loading a pretrained modernbert
config_path = "/s2_nfs/ckpt/flex-bert-modernbert-base-edu-fw-320B-paper-custom-tokenizer.yaml"
checkpoint_path = "/s2_nfs/ckpt/flex-bert-modernbert-base-edu-fw-320B-article-custom-tokenizer/ep3-ba339282-rank0.pt"

cfg = OmegaConf.load(config_path)

checkpoint = torch.load(checkpoint_path, map_location="cpu")
state_dict = checkpoint.get("state", {}).get("model", {})

### Option1: create and load a mlm model and use only ther bert part of it (model.model.bert(**inputs))
model = create_flex_bert_mlm(
    pretrained_model_name=cfg.model.pretrained_model_name,
    model_config=cfg.model.model_config,
    tokenizer_name=cfg.model.tokenizer_name,
    gradient_checkpointing=cfg.model.get("gradient_checkpointing", False)
)


### Option2: create and load a base model (model(**inputs))
# pretrained_model_name=cfg.model.pretrained_model_name
# model_config=cfg.model.model_config
# if isinstance(model_config, DictConfig):
#     model_config = OmegaConf.to_container(model_config, resolve=True)
# config = configuration_bert_module.FlexBertConfig.from_pretrained(pretrained_model_name, **model_config)
# config.vocab_size = 50368
# model = FlexBertModel(config)
# state_dict = {k: v for k, v in state_dict.items() if k.startswith("model.bert.")}
# state_dict = {k[len("model.bert."):]: v for k, v in state_dict.items()}


model.load_state_dict(state_dict)
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
model = model.to(device)
model.eval()

print(f"Model loaded successfully on {device}")
print(f"Model configuration: {cfg.model.model_config}")