#!/usr/bin/env python3
"""
Test script to verify ROTALI Flash Attention dtype conversion works correctly.

This script specifically tests that the padded ROTALI attention handles
dtype conversion properly for Flash Attention compatibility.
"""

import sys
import os
import torch
import torch.nn as nn

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_rotali_flash_attention_fp32():
    """Test ROTALI with Flash Attention using fp32 input (should convert to bf16)."""
    print("Testing ROTALI Flash Attention with fp32 input...")

    # Skip if CUDA is not available
    if not torch.cuda.is_available():
        print("⚠️  Skipping Flash Attention test - CUDA not available")
        return True

    try:
        from src.bert_layers.configuration_bert import FlexBertConfig
        from src.bert_layers.rotali_attention import FlexBertRotaliAttention

        config = FlexBertConfig(
            hidden_size=768,
            num_attention_heads=12,
            use_rotali=True,
            use_fa2=True,  # Enable Flash Attention
            attention_probs_dropout_prob=0.0
        )

        attention = FlexBertRotaliAttention(config, layer_id=0)
        attention.eval()  # Set to eval mode
        attention = attention.cuda()  # Move to CUDA

        # Create test input in fp32 on CUDA
        batch_size, seq_len = 2, 16
        hidden_states = torch.randn(batch_size, seq_len, config.hidden_size, dtype=torch.float32, device='cuda')

        print(f"Input dtype: {hidden_states.dtype}, device: {hidden_states.device}")

        # Forward pass
        with torch.no_grad():
            output = attention(hidden_states)

        print(f"✓ Flash Attention forward pass successful with fp32 input")
        print(f"  - Input shape: {hidden_states.shape}")
        print(f"  - Output shape: {output.shape}")
        print(f"  - Input dtype: {hidden_states.dtype}")
        print(f"  - Output dtype: {output.dtype}")

        # Check output shape and dtype
        assert output.shape == hidden_states.shape, f"Shape mismatch: {output.shape} vs {hidden_states.shape}"
        assert output.dtype == hidden_states.dtype, f"Dtype mismatch: {output.dtype} vs {hidden_states.dtype}"

        print("✓ Output shape and dtype match input")
        return True

    except Exception as e:
        print(f"✗ Error in Flash Attention fp32 test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_rotali_flash_attention_bf16():
    """Test ROTALI with Flash Attention using bf16 input (should work directly)."""
    print("\nTesting ROTALI Flash Attention with bf16 input...")

    # Skip if CUDA is not available
    if not torch.cuda.is_available():
        print("⚠️  Skipping Flash Attention test - CUDA not available")
        return True

    try:
        from src.bert_layers.configuration_bert import FlexBertConfig
        from src.bert_layers.rotali_attention import FlexBertRotaliAttention

        config = FlexBertConfig(
            hidden_size=768,
            num_attention_heads=12,
            use_rotali=True,
            use_fa2=True,  # Enable Flash Attention
            attention_probs_dropout_prob=0.0
        )

        attention = FlexBertRotaliAttention(config, layer_id=0)
        attention.eval()  # Set to eval mode
        attention = attention.cuda().to(torch.bfloat16)  # Move to CUDA and convert to bf16

        # Create test input in bf16 on CUDA
        batch_size, seq_len = 2, 16
        hidden_states = torch.randn(batch_size, seq_len, config.hidden_size, dtype=torch.bfloat16, device='cuda')

        print(f"Input dtype: {hidden_states.dtype}, device: {hidden_states.device}")

        # Forward pass
        with torch.no_grad():
            output = attention(hidden_states)

        print(f"✓ Flash Attention forward pass successful with bf16 input")
        print(f"  - Input shape: {hidden_states.shape}")
        print(f"  - Output shape: {output.shape}")
        print(f"  - Input dtype: {hidden_states.dtype}")
        print(f"  - Output dtype: {output.dtype}")

        # Check output shape and dtype
        assert output.shape == hidden_states.shape, f"Shape mismatch: {output.shape} vs {hidden_states.shape}"
        assert output.dtype == hidden_states.dtype, f"Dtype mismatch: {output.dtype} vs {hidden_states.dtype}"

        print("✓ Output shape and dtype match input")
        return True

    except Exception as e:
        print(f"✗ Error in Flash Attention bf16 test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_rotali_sdpa_fallback():
    """Test ROTALI with SDPA fallback when Flash Attention is disabled."""
    print("\nTesting ROTALI SDPA fallback...")
    
    try:
        from src.bert_layers.configuration_bert import FlexBertConfig
        from src.bert_layers.rotali_attention import FlexBertRotaliAttention
        
        config = FlexBertConfig(
            hidden_size=768,
            num_attention_heads=12,
            use_rotali=True,
            use_fa2=False,  # Disable Flash Attention
            attention_probs_dropout_prob=0.0
        )
        
        attention = FlexBertRotaliAttention(config, layer_id=0)
        attention.eval()  # Set to eval mode
        
        # Create test input in fp32
        batch_size, seq_len = 2, 16
        hidden_states = torch.randn(batch_size, seq_len, config.hidden_size, dtype=torch.float32)
        
        print(f"Input dtype: {hidden_states.dtype}")
        
        # Forward pass
        with torch.no_grad():
            output = attention(hidden_states)
        
        print(f"✓ SDPA forward pass successful")
        print(f"  - Input shape: {hidden_states.shape}")
        print(f"  - Output shape: {output.shape}")
        print(f"  - Input dtype: {hidden_states.dtype}")
        print(f"  - Output dtype: {output.dtype}")
        
        # Check output shape and dtype
        assert output.shape == hidden_states.shape, f"Shape mismatch: {output.shape} vs {hidden_states.shape}"
        assert output.dtype == hidden_states.dtype, f"Dtype mismatch: {output.dtype} vs {hidden_states.dtype}"
        
        print("✓ Output shape and dtype match input")
        return True
        
    except Exception as e:
        print(f"✗ Error in SDPA test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run Flash Attention dtype tests."""
    print("=" * 60)
    print("ROTALI Flash Attention Dtype Test Suite")
    print("=" * 60)
    
    tests = [
        test_rotali_flash_attention_fp32,
        test_rotali_flash_attention_bf16,
        test_rotali_sdpa_fallback,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    print("=" * 60)
    
    if passed == total:
        print("🎉 All Flash Attention dtype tests passed!")
        return 0
    else:
        print("❌ Some Flash Attention tests failed.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
